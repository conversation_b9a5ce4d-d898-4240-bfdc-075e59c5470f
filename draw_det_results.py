import os
import json
import cv2
import random

# 所有类别
CLASSES = [
    "GMDK", "GDYC", "KJQS", "KJWX", "KJDL",
    "TTQS", "TTSD", "TTDL", "DBWX", "LSQS",
    "GZDK", "DCLW", "GDYW", "JB", "PQ"
]

# 为每个类别生成固定颜色
def generate_class_colors(class_list):
    random.seed(42)
    colors = {}
    for cls in class_list:
        colors[cls] = tuple(random.randint(0, 255) for _ in range(3))
    return colors

# 配置路径
json_dir = "/home/<USER>/workspace/maxi/test_results/T-R"
output_dir = "/home/<USER>/workspace/maxi/det_results/img_results"

os.makedirs(output_dir, exist_ok=True)
CLASS_COLORS = generate_class_colors(CLASSES)

# 样式参数
BOX_THICKNESS = 5           # 边框厚度
FONT_SCALE = 1.2            # 字体大小
FONT_THICKNESS = 2          # 字体粗细
FONT = cv2.FONT_HERSHEY_SIMPLEX

for filename in os.listdir(json_dir):
    if filename.endswith(".json"):
        json_path = os.path.join(json_dir, filename)
        with open(json_path, 'r') as f:
            data = json.load(f)

        image_path = data.get("image_path")
        objects = data.get("objects", [])

        if not os.path.exists(image_path):
            print(f"[警告] 找不到图片: {image_path}")
            continue

        image = cv2.imread(image_path)
        if image is None:
            print(f"[错误] 无法读取图片: {image_path}")
            continue

        for obj in objects:
            left, top, right, bottom = obj['left'], obj['top'], obj['right'], obj['bottom']
            class_name = obj['class_name']
            confidence = obj['confidence']
            color = CLASS_COLORS.get(class_name, (255, 255, 255))

            # 绘制边框
            cv2.rectangle(image, (left, top), (right, bottom), color, thickness=BOX_THICKNESS)

            # 标签内容
            label = f"{class_name} {confidence:.2f}"
            (text_w, text_h), baseline = cv2.getTextSize(label, FONT, FONT_SCALE, FONT_THICKNESS)
            text_x, text_y = left, top - 10 if top - 10 > text_h else top + text_h + 10

            # 绘制背景框（比文字略大）
            cv2.rectangle(image, (text_x, text_y - text_h - 5), (text_x + text_w + 5, text_y + baseline), color, thickness=-1)

            # 写文字（白色字体）
            cv2.putText(image, label, (text_x + 2, text_y - 2), FONT, FONT_SCALE, (255, 255, 255), FONT_THICKNESS, lineType=cv2.LINE_AA)

        # 保存结果
        output_path = os.path.join(output_dir, os.path.basename(image_path))
        cv2.imwrite(output_path, image)
        print(f"[完成] 保存标注图像: {output_path}")
