#include "mpf/daemon_process.h"
#include <iostream>
#include <boost/program_options.hpp>

namespace po = boost::program_options;

/**
 * @brief 解析命令行参数
 *
 * @param argc 参数数量
 * @param argv 参数数组
 * @param listenPort 输出参数，监听端口
 * @param configFile 输出参数，配置文件路径
 * @param daemonize 输出参数，是否守护进程化
 * @return true 解析成功
 * @return false 解析失败
 */
bool parseCommandLine(int argc, char* argv[], int& listenPort, std::string& configFile, bool& daemonize) {
    try {
        // 解析命令行参数
        po::options_description desc("允许的选项");
        desc.add_options()
            ("help", "显示帮助信息")
            ("port", po::value<int>(&listenPort)->default_value(9000), "监听端口")
            ("config", po::value<std::string>(&configFile)->default_value("config.json"), "配置文件路径")
            ("daemon", po::value<bool>(&daemonize)->default_value(true), "是否以守护进程方式运行");

        po::variables_map vm;
        po::store(po::parse_command_line(argc, argv, desc), vm);
        po::notify(vm);

        if (vm.count("help")) {
            std::cout << desc << std::endl;
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "命令行解析错误: " << e.what() << std::endl;
        return false;
    }
}

int main(int argc, char* argv[]) {
    try {
        // 解析命令行参数
        int listenPort;
        std::string configFile;
        bool daemonize;

        if (!parseCommandLine(argc, argv, listenPort, configFile, daemonize)) {
            return 1;
        }

        // 创建守护进程
        mpf::DaemonProcess daemon(listenPort, configFile);

        // 运行守护进程
        if (daemonize) {
            return daemon.run();
        } else {
            // 非守护进程模式，直接启动
            if (!daemon.start()) {
                std::cerr << "启动守护进程失败" << std::endl;
                return 1;
            }

            std::cout << "守护进程已启动（非守护进程模式），按Ctrl+C退出" << std::endl;

            // 等待信号
            sigset_t mask;
            sigemptyset(&mask);
            sigaddset(&mask, SIGINT);
            sigaddset(&mask, SIGTERM);

            // 阻塞这些信号，以便我们可以等待它们
            sigprocmask(SIG_BLOCK, &mask, NULL);

            // 等待信号
            int sig;
            sigwait(&mask, &sig);

            std::cout << "接收到信号 " << sig << "，准备退出..." << std::endl;
            daemon.stop();

            return 0;
        }
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
}
