# 多进程框架项目架构说明

## 1. 项目概述

多进程框架（Multi-Process Framework，简称MPF）是一个用于图像处理的高性能并行计算框架。该框架采用多进程架构，能够充分利用多核CPU和多GPU资源，加速大规模图像处理任务。框架支持任务的自动分配、进度监控和结果汇总，并提供完善的日志记录功能。现已集成YOLOv8目标检测算法，能够高效处理大量图像并输出检测结果。

## 2. 目录结构

项目采用模块化设计，目录结构清晰，各个组件职责明确：

```
maxi/
├── app/                    # 应用程序入口
│   ├── daemon.cpp          # 守护进程入口
│   ├── worker_main.cpp     # 工作进程入口
│   └── multi_gpu_main.cpp  # 多GPU测试程序入口
├── include/                # 头文件目录
│   ├── mpf/                # 框架头文件
│   │   ├── config_parser.h # 配置解析模块
│   │   ├── daemon_process.h # 守护进程模块
│   │   ├── gpu_detector.h  # GPU检测模块
│   │   ├── main_process.h  # 主进程模块
│   │   ├── message.h       # 消息模块
│   │   ├── task_manager.h  # 任务管理模块
│   │   └── worker_process.h # 工作进程模块
│   ├── utils/              # 工具类头文件
│   │   └── logger.h        # 日志模块
│   └── detect/             # 检测模块头文件
│       ├── test.hpp        # YOLOv8测试类
│       ├── infer.hpp       # 推理引擎接口
│       └── common.hpp      # 公共定义
├── lib/                    # 库文件目录
│   ├── mpf/                # 框架实现文件
│   │   ├── config_parser.cpp # 配置解析模块实现
│   │   ├── daemon_process.cpp # 守护进程模块实现
│   │   ├── gpu_detector.cpp # GPU检测模块实现
│   │   ├── main_process.cpp # 主进程模块实现
│   │   ├── message.cpp     # 消息模块实现
│   │   ├── task_manager.cpp # 任务管理模块实现
│   │   └── worker_process.cpp # 工作进程模块实现
│   └── detect/             # 检测模块实现文件
│       ├── test.cpp        # YOLOv8测试类实现
│       ├── infer.cpp       # 推理引擎接口实现
│       └── common.cpp      # 公共定义实现
├── client_test/            # 客户端测试程序
│   ├── client_test.sh      # 客户端测试脚本
│   └── README.md           # 客户端说明文档
├── build/                  # 构建输出目录
├── CMakeLists.txt          # CMake构建配置
├── config.json             # 配置文件
└── docs/                   # 文档目录
    ├── structure.md        # 项目架构说明文档
    └── test_daemon.md      # 守护进程测试文档
```

## 3. 核心模块说明

### 3.1 消息模块 (message.h/cpp)

消息模块负责进程间通信的消息序列化和反序列化，是整个框架的通信基础。

- **主要功能**：
  - 定义进程间通信的消息结构
  - 提供消息序列化为JSON的功能
  - 提供从JSON反序列化为消息的功能

- **核心类/结构**：
  - `ProcessMessage`：消息结构体，包含消息类型、进程ID、数据和计数
  - `serializeMessage()`：将消息序列化为JSON字符串
  - `deserializeMessage()`：从JSON字符串反序列化消息

- **消息类型**：
  - `TASK`：主进程发送任务给工作进程
  - `PROGRESS`：工作进程报告处理进度
  - `COMPLETE`：工作进程报告任务完成
  - `TERMINATE`：主进程通知工作进程终止
  - `CONFIG`：主进程发送配置信息给工作进程

### 3.2 日志模块 (logger.h/cpp)

日志模块提供统一的日志记录功能，支持不同级别的日志输出和多种输出目标。

- **主要功能**：
  - 初始化主进程和工作进程的日志系统
  - 提供多级别的日志记录功能
  - 支持同时输出到控制台和文件

- **核心类**：
  - `Logger`：日志管理类，提供静态方法初始化和获取日志记录器

### 3.3 配置解析模块 (config_parser.h/cpp)

配置解析模块负责解析JSON格式的配置文件，为框架提供配置信息。

- **主要功能**：
  - 加载和解析JSON配置文件
  - 提供类型安全的配置项访问方法
  - 支持默认值和错误处理

- **核心类**：
  - `ConfigParser`：配置解析类，提供配置文件加载和解析功能

### 3.4 任务管理模块 (task_manager.h/cpp)

任务管理模块负责收集和分发任务，以及处理单个任务的执行。

- **主要功能**：
  - 收集指定目录下的图片路径
  - 将图片路径分配给多个进程
  - 处理单个图片或批量图片，生成JSON结果文件

- **核心类**：
  - `TaskManager`：任务管理类，提供任务收集、分发和处理功能
  - `YoloConfig`：YOLOv8配置结构体，存储YOLOv8相关配置

### 3.5 GPU检测模块 (gpu_detector.h/cpp)

GPU检测模块负责检测系统中的GPU资源，为任务分配提供硬件信息支持。

- **主要功能**：
  - 检测系统中的GPU数量和信息
  - 提供GPU信息给主进程

- **核心类**：
  - `GpuDetector`：GPU检测类，提供静态方法检测GPU

### 3.6 工作进程模块 (worker_process.h/cpp)

工作进程模块负责执行具体的任务，并向主进程报告进度和结果。

- **主要功能**：
  - 初始化工作进程
  - 接收主进程分配的任务和配置信息
  - 处理任务并报告进度
  - 响应主进程的终止信号

- **核心类**：
  - `WorkerProcess`：工作进程类，负责任务处理和进度报告

### 3.7 主进程模块 (main_process.h/cpp)

主进程模块负责管理工作进程、分发任务和监控进度。

- **主要功能**：
  - 启动和管理工作进程
  - 分发任务和配置信息给工作进程
  - 监控工作进程的进度
  - 终止工作进程

- **核心类**：
  - `MainProcess`：主进程类，负责整个框架的协调和管理

### 3.8 守护进程模块 (daemon_process.h/cpp)

守护进程模块负责以守护进程方式运行，接收客户端请求并启动处理任务。

- **主要功能**：
  - 以守护进程方式运行
  - 监听网络端口，接收客户端请求
  - 启动主进程处理任务
  - 向客户端报告处理进度和结果

- **核心类**：
  - `DaemonProcess`：守护进程类，负责接收请求和启动处理

### 3.9 检测模块 (detect/)

检测模块集成了YOLOv8目标检测算法，负责图像的目标检测任务。

- **主要功能**：
  - 加载YOLOv8模型和配置
  - 执行目标检测推理
  - 输出检测结果到JSON文件

- **核心类**：
  - `Test`：YOLOv8测试类，提供图像处理和推理功能
  - `Infer`：推理引擎接口，封装TensorRT推理功能

## 4. 通信机制

框架采用ZeroMQ库实现进程间通信，使用Router-Dealer模式：

- **主进程**：使用Router套接字，可以向特定的工作进程发送消息
- **工作进程**：使用Dealer套接字，连接到主进程
- **守护进程**：使用TCP套接字，接收客户端请求

消息通过JSON格式序列化，支持多种类型的消息，包括任务分配、进度报告、配置信息等。

## 5. 多GPU支持

框架支持多GPU并行处理，通过以下机制实现：

- **GPU检测**：自动检测系统中的GPU数量和信息
- **GPU分配**：根据配置文件中的`gpu_ids`设置或客户端请求中的`gpu_id`参数分配工作进程
- **GPU绑定**：每个工作进程通过环境变量`CUDA_VISIBLE_DEVICES`绑定到特定的GPU
- **配置传递**：主进程通过ZMQ消息将YOLOv8配置信息传递给工作进程

## 6. 任务分发与执行流程

1. **客户端请求阶段**：
   - 客户端发送处理请求到守护进程
   - 守护进程解析请求，获取输入目录和输出目录

2. **启动阶段**：
   - 守护进程创建主进程
   - 主进程检测GPU资源
   - 收集输入目录中的图片路径
   - 启动工作进程，每个工作进程绑定到一个GPU

3. **配置传递阶段**：
   - 主进程从配置文件中读取YOLOv8配置
   - 主进程将配置信息发送给所有工作进程
   - 工作进程接收配置信息并设置YOLOv8环境

4. **任务分发阶段**：
   - 主进程将图片路径平均分配给各个工作进程
   - 通过ZMQ发送任务消息

5. **任务执行阶段**：
   - 工作进程接收任务并处理图片
   - 工作进程使用YOLOv8进行目标检测
   - 工作进程将检测结果保存为JSON文件
   - 工作进程定期向主进程报告进度
   - 完成所有任务后报告完成

6. **监控阶段**：
   - 主进程接收工作进程的进度报告
   - 计算总体进度和处理速度
   - 输出进度信息到日志
   - 向守护进程报告进度

7. **终止阶段**：
   - 所有任务完成后，主进程发送终止信号给工作进程
   - 工作进程接收终止信号后退出
   - 主进程等待所有工作进程结束后退出
   - 守护进程向客户端报告处理完成

## 7. 构建系统

项目使用CMake作为构建系统，支持跨平台构建：

- **静态库**：将核心功能编译为静态库
- **动态库**：将YOLOv8检测功能编译为动态库libyolo_infer.so
- **可执行文件**：编译守护进程、工作进程和测试程序
- **依赖管理**：自动配置第三方依赖，如ZeroMQ、Boost、OpenCV等

## 8. 配置文件

框架使用JSON格式的配置文件，支持以下配置项：

```json
{
    "process": {
        "batch_size": 1000
    },
    "paths": {
        "model_config_dir": "/home/<USER>/workspace/maxi/model_config.json",
        "input_dir": "/home/<USER>/workspace/maxi/test_large_imgs",
        "output_dir": "/home/<USER>/workspace/maxi/test_results",
        "log_file": "multi_process_framework.log"
    },
    "gpu": {
        "gpu_ids": [0, 1]
    },
    "windows_reporter": {
        "enabled": true,
        "host": "127.0.0.1",
        "port": 8080,
        "report_interval_ms": 1000
    },
    "yolo": {
        "engine_file": "/home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.transd.engine",
        "config_file": "/home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.cfg",
        "task_name": "yolov8_detection",
        "log_file": "yolov8_detection.log"
    }
}
```

## 9. 扩展性设计

框架设计考虑了扩展性，可以通过以下方式扩展：

1. **添加新的检测算法**：扩展检测模块，添加新的检测算法
2. **增强GPU支持**：扩展GPU检测模块，添加更多GPU相关功能
3. **优化调度算法**：修改任务分发逻辑，实现更智能的负载均衡
4. **添加新的通信模式**：扩展消息模块，支持更多的通信模式

## 10. 总结

多进程框架采用模块化设计，各个组件职责明确，通过ZeroMQ实现高效的进程间通信，通过Boost.Process管理子进程。框架集成了YOLOv8目标检测算法，支持多GPU并行处理，具有良好的扩展性和可维护性，能够满足大规模图像处理的需求。
