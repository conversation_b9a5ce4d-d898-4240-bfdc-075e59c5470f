# 环境配置指南

本文档详细说明了多进程图像处理框架的环境配置步骤，包括系统依赖、NVIDIA驱动、CUDA、cuDNN、TensorRT和其他必要库的安装。

## 目录

1. [系统要求](#1-系统要求)
2. [APT源配置](#2-apt源配置)
3. [NVIDIA驱动安装](#3-nvidia驱动安装)
4. [CUDA安装](#4-cuda安装)
5. [cuDNN安装](#5-cudnn安装)
6. [TensorRT安装](#6-tensorrt安装)
7. [依赖库安装](#7-依赖库安装)
8. [环境验证](#8-环境验证)
9. [卸载指南](#9-卸载指南)

## 1. 系统要求

- **操作系统**: Ubuntu 20.04 LTS
- **GPU**: NVIDIA GeForce RTX 3090或其他支持CUDA的NVIDIA GPU
- **磁盘空间**: 至少20GB可用空间
- **内存**: 至少16GB RAM

## 2. APT源配置

为加速下载，可以配置国内APT源：

1. 备份原有源配置：

```shell
sudo cp /etc/apt/sources.list /etc/apt/sources.list.bak
```

2. 使用清华源（以Ubuntu 22.04为例）：

```shell
sudo tee /etc/apt/sources.list > /dev/null << EOF
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy main restricted universe multiverse
# deb-src https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy main restricted universe multiverse
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy-updates main restricted universe multiverse
# deb-src https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy-updates main restricted universe multiverse
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy-backports main restricted universe multiverse
# deb-src https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy-backports main restricted universe multiverse
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy-security main restricted universe multiverse
# deb-src https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy-security main restricted universe multiverse
EOF
```

3. 更新软件包列表：

```shell
sudo apt-get update
```

## 3. NVIDIA驱动安装

1. 查看推荐的驱动版本：

```shell
ubuntu-drivers devices
```

输出示例：
```
== /sys/devices/pci0000:00/0000:00:01.0/0000:01:00.0 ==
modalias : pci:v000010DEd00002204sv000010DEsd00001454bc03sc00i00
vendor   : NVIDIA Corporation
model    : GA102 [GeForce RTX 3090]
driver   : nvidia-driver-570-open - distro non-free
driver   : nvidia-driver-545-open - distro non-free
driver   : nvidia-driver-550-open - distro non-free
driver   : nvidia-driver-550 - distro non-free
driver   : nvidia-driver-570 - distro non-free recommended
driver   : nvidia-driver-535-server - distro non-free
driver   : nvidia-driver-520 - third-party non-free
driver   : nvidia-driver-470-server - distro non-free
driver   : nvidia-driver-535-open - distro non-free
driver   : nvidia-driver-535-server-open - distro non-free
driver   : nvidia-driver-570-server-open - distro non-free
driver   : nvidia-driver-570-server - distro non-free
driver   : nvidia-driver-470 - distro non-free
driver   : nvidia-driver-545 - distro non-free
driver   : nvidia-driver-535 - distro non-free
driver   : xserver-xorg-video-nouveau - distro free builtin
```

2. 安装驱动：

> **重要提示**: CUDA 11.8 需要 520 或更高版本的驱动。如果没有 520 版本，可以安装 535 版本。

```shell
sudo apt install nvidia-driver-535
```

3. 重启系统：

```shell
sudo reboot
```

4. 验证驱动安装：

```shell
nvidia-smi
```

输出示例：
```
Mon May 12 13:07:13 2025
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.230.02             Driver Version: 535.230.02   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA GeForce RTX 3090        Off | 00000000:01:00.0 Off |                  N/A |
|  0%   43C    P8              15W / 370W |    220MiB / 24576MiB |      0%      Default |
|                                         |                      |                  N/A |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA GeForce RTX 3090        Off | 00000000:04:00.0 Off |                  N/A |
| 42%   25C    P8               9W / 350W |     16MiB / 24576MiB |      0%      Default |
|                                         |                      |                  N/A |
+-----------------------------------------+----------------------+----------------------+
```

## 4. CUDA安装

1. 下载CUDA 11.8安装包：

从NVIDIA官网下载：https://developer.nvidia.com/cuda-toolkit-archive

> **重要提示**: 必须使用 `cuda_11.8.0_520.61.05_linux.run` 安装包

2. 安装CUDA（跳过驱动安装）：

```shell
chmod 755 cuda_11.8.0_520.61.05_linux.run
sudo sh cuda_11.8.0_520.61.05_linux.run --toolkit --silent --override
```

3. 配置环境变量：

编辑 `~/.bashrc` 文件，添加以下内容：

```shell
export PATH=/usr/local/cuda-11.8/bin:$PATH
export LD_LIBRARY_PATH=/usr/local/cuda-11.8/lib64:$LD_LIBRARY_PATH
```

应用更改：

```shell
source ~/.bashrc
```

4. 验证CUDA安装：

```shell
nvcc -V
```

输出示例：
```
nvcc: NVIDIA (R) Cuda compiler driver
Copyright (c) 2005-2022 NVIDIA Corporation
Built on Wed_Sep_21_10:33:58_PDT_2022
Cuda compilation tools, release 11.8, V11.8.89
Build cuda_11.8.r11.8/compiler.31833905_0
```

## 5. cuDNN安装

1. 下载cuDNN：

从NVIDIA开发者网站下载cuDNN 8.6.0（需要注册NVIDIA开发者账号）：
https://developer.nvidia.com/cudnn

2. 解压并安装：

```shell
tar -xf cudnn-linux-x86_64-8.6.0.163_cuda11-archive.tar.xz
cd cudnn-linux-x86_64-8.6.0.163_cuda11-archive

# 复制文件到CUDA目录
sudo cp include/cudnn*.h /usr/local/cuda-11.8/include/
sudo cp lib/libcudnn* /usr/local/cuda-11.8/lib64/
sudo chmod a+r /usr/local/cuda-11.8/include/cudnn*.h /usr/local/cuda-11.8/lib64/libcudnn*
```

## 6. TensorRT安装

1. 下载TensorRT：

从NVIDIA开发者网站下载TensorRT 8.5.3.1（需要注册NVIDIA开发者账号）：
https://developer.nvidia.com/tensorrt

2. 解压TensorRT：

```shell
tar -zxvf TensorRT-8.5.3.1.Linux.x86_64-gnu.cuda-11.8.cudnn8.6.tar.gz
```

3. 安装TensorRT：

```shell
# 进入解压目录
cd TensorRT-8.5.3.1

# 复制库文件
sudo cp -r lib/* /usr/local/cuda-11.8/lib64/

# 复制头文件
sudo cp -r include/* /usr/local/cuda-11.8/include/

# 复制二进制工具
sudo cp -r bin/* /usr/local/cuda-11.8/bin/
```

4. 配置动态链接库：

```shell
# 添加库路径到系统配置
echo '/usr/local/cuda-11.8/lib64' | sudo tee /etc/ld.so.conf.d/cuda-tensorrt.conf
sudo ldconfig
```

5. 验证TensorRT安装：

```shell
cd /path/to/TensorRT-8.5.3.1/samples/sampleOnnxMNIST
make -j$(nproc)
cd ../../bin
./sample_onnx_mnist
```

## 7. 依赖库安装

安装项目所需的其他依赖库：

```shell
# 安装OpenCV
sudo apt install libopencv-dev

# 安装Boost库
sudo apt install libboost-all-dev -y

# 安装JSON库
sudo apt install nlohmann-json3-dev

# 安装ZeroMQ
sudo apt install libzmq3-dev libczmq-dev

# 安装spdlog
sudo apt install libspdlog-dev

# 安装fmt
sudo apt install libfmt-dev

# 安装CMake（如果尚未安装）
sudo apt install cmake build-essential
```

## 8. 环境验证

编译并运行项目以验证环境配置：

```shell
# 克隆项目（如果尚未克隆）
git clone https://github.com/your-username/maxi.git
cd maxi

# 创建构建目录
mkdir -p build && cd build

# 配置和编译
cmake ..
make -j$(nproc)

# 运行测试程序
./yolov8-multi-gpu --help
```

## 9. 卸载指南

如果需要卸载或重新安装，可以按照以下步骤操作：

### 卸载NVIDIA驱动

```shell
sudo apt-get purge nvidia-*
sudo apt-get autoremove
sudo apt-get autoclean
```

### 卸载CUDA

```shell
sudo apt-get purge cuda-*
sudo rm -rf /usr/local/cuda-11.8
```

### 检查残留

```shell
dpkg -l | grep nvidia
dpkg -l | grep cuda
```

## 附录：版本兼容性

| 组件 | 版本 | 兼容性说明 |
|------|------|------------|
| NVIDIA驱动 | 520+ | CUDA 11.8需要520或更高版本 |
| CUDA | 11.8 | 项目指定使用CUDA 11.8 |
| cuDNN | 8.6.0 | 与CUDA 11.8兼容 |
| TensorRT | 8.5.3.1 | 与CUDA 11.8和cuDNN 8.6兼容 |
| Ubuntu | 22.04 LTS | 推荐使用的操作系统版本 |
