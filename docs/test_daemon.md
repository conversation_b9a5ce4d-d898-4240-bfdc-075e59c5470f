# 守护进程测试文档

本文档详细说明了如何测试图像处理守护进程及其客户端。测试包括守护进程的启动、停止、状态查询以及图像处理任务的执行。

## 1. 编译项目

首先，确保项目已正确编译：

```bash
# 在项目根目录下
mkdir -p build && cd build
cmake ..
make -j$(nproc)
```

## 2. 配置文件准备

确保配置文件 `config.json` 已正确设置：

```bash
# 在项目根目录下
cat config.json
```

配置文件内容应类似于：

```json
{
    "process": {
        "batch_size": 1000
    },
    "paths": {
        "model_config_dir": "/home/<USER>/workspace/maxi/model_config.json",
        "input_dir": "/home/<USER>/workspace/maxi/test_large_imgs",
        "output_dir": "/home/<USER>/workspace/maxi/test_results",
        "log_file": "multi_process_framework.log"
    },
    "gpu": {
        "gpu_ids": [0, 1]
    },
    "windows_reporter": {
        "enabled": true,
        "host": "127.0.0.1",
        "port": 8080,
        "report_interval_ms": 1000
    },
    "yolo": {
        "engine_file": "/home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.transd.engine",
        "config_file": "/home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.cfg",
        "task_name": "yolov8_detection",
        "log_file": "yolov8_detection.log"
    }
}
```

## 3. 测试守护进程

### 3.1 以非守护进程模式启动（调试模式）

首先，以非守护进程模式启动，这样可以直接在控制台看到输出：

```bash
# 在build目录下
./daemon_process --port 9000 --config ../config.json --daemon false
```

应该看到类似以下输出：

```
[2025-05-21 16:15:00.672] [info] [421813] 日志系统初始化完成，日志文件: daemon_process.log
[2025-05-21 16:15:00.672] [info] [421813] 成功加载配置文件: ../config.json
[2025-05-21 16:15:00.672] [info] [421813] PID文件已创建: /tmp/daemon_process.pid
[2025-05-21 16:15:00.672] [info] [421813] 守护进程已启动，监听端口: 9000
守护进程已启动（非守护进程模式），按Ctrl+C退出
[2025-05-21 16:15:00.672] [info] [421814] 监听线程已启动，等待客户端连接...
```

此时，守护进程在前台运行，可以看到所有日志输出。按 Ctrl+C 可以停止进程。

### 3.2 以守护进程模式启动

接下来，以守护进程模式启动：

```bash
# 在build目录下
./daemon_process --port 9000 --config ../config.json
```

此时，进程会在后台运行，没有控制台输出。

### 3.3 验证守护进程是否在运行

使用以下命令验证守护进程是否在运行：

```bash
# 查看PID文件
cat /tmp/daemon_process.pid

# 使用PID检查进程状态
ps -p $(cat /tmp/daemon_process.pid)
```

如果守护进程正在运行，应该能看到进程信息。

也可以检查日志文件：

```bash
# 在build目录下
cat daemon_process.log
```

日志应该包含启动信息和监听端口信息。

## 4. 测试客户端

### 4.1 使用测试脚本

最简单的测试方法是使用提供的客户端测试脚本：

```bash
# 在项目根目录下
./client_test.sh
```

这个脚本会向守护进程发送处理请求，处理指定目录下的图片。

### 4.2 观察处理过程

在守护进程的日志中，你应该能看到类似以下输出：

```
[2025-05-21 16:34:07.015] [info] [427096] 接收到来自 127.0.0.1 的连接
[2025-05-21 16:34:07.015] [info] [427096] 接收到请求: {"input_dir":"/home/<USER>/workspace/maxi/test_large_imgs","output_dir":"/home/<USER>/workspace/maxi/test_results","type":"process_images"}
[2025-05-21 16:34:07.015] [info] [427096] 设置 YOLOv8 配置...
[2025-05-21 16:34:07.015] [info] [427096] YOLOv8 配置已设置
[2025-05-21 16:34:07.015] [info] [427096] 引擎文件: /home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.transd.engine
[2025-05-21 16:34:07.015] [info] [427096] 配置文件: /home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.cfg
[2025-05-21 16:34:07.015] [info] [427096] 任务名称: yolov8_detection
[2025-05-21 16:34:07.015] [info] [427096] 日志文件: yolov8_detection.log
[2025-05-21 16:34:07.017] [info] [427096] 日志系统初始化完成，日志文件: multi_process_framework.log
[2025-05-21 16:34:07.042] [info] [427118] 检测到 2 个GPU
[2025-05-21 16:34:07.043] [info] [427118] GPU信息: 2, NVIDIA GeForce RTX 3090, 24576 MiB
[2025-05-21 16:34:07.043] [info] [427118] 使用的 GPU IDs = 0 1
[2025-05-21 16:34:07.043] [info] [427118] 根据GPU数量设置进程数为 2
[2025-05-21 16:34:07.043] [info] [427118] 启动 2 个工作进程
[2025-05-21 16:34:07.043] [info] [427118] 输入目录: /home/<USER>/workspace/maxi/test_large_imgs
[2025-05-21 16:34:07.043] [info] [427118] 输出目录: /home/<USER>/workspace/maxi/test_results
[2025-05-21 16:34:07.043] [info] [427118] 每批任务数量: 1000
[2025-05-21 16:34:07.043] [info] [427118] 正在收集图片路径...
[2025-05-21 16:34:07.050] [info] [427118] 找到 630 张图片
[2025-05-21 16:34:07.050] [info] [427118] 根据指定的GPU ID启动工作进程
[2025-05-21 16:34:07.050] [info] [427118] 启动工作进程 0 在 GPU 0 上，日志文件: worker_gpu0.log
[2025-05-21 16:34:07.054] [info] [427118] 启动工作进程 1 在 GPU 1 上，日志文件: worker_gpu1.log
[2025-05-21 16:34:08.059] [info] [427118] 初始分发任务：总任务数 630，每个进程分配 315 个任务
[2025-05-21 16:34:08.064] [info] [427118] 发送了 315 个任务给进程 0
[2025-05-21 16:34:08.066] [info] [427118] 发送了 315 个任务给进程 1
```

### 4.3 检查工作进程日志

工作进程的日志文件包含每个进程的详细处理信息：

```bash
# 在build目录下
cat worker_gpu0.log
cat worker_gpu1.log
```

### 4.4 检查处理结果

处理完成后，检查输出目录中的结果：

```bash
# 在项目根目录下
ls -la test_results/T-L
ls -la test_results/T-M
ls -la test_results/T-R
```

每个图片应该有一个对应的JSON文件，包含检测结果。

## 5. 多GPU测试

框架支持多GPU并行处理，可以通过以下方式测试：

### 5.1 检查GPU配置

确保配置文件中的`gpu_ids`设置正确：

```json
"gpu": {
    "gpu_ids": [0, 1]
}
```

### 5.2 观察GPU使用情况

在处理过程中，可以使用`nvidia-smi`命令观察GPU使用情况：

```bash
watch -n 1 nvidia-smi
```

应该能看到多个GPU都在被使用。

## 6. 停止守护进程

处理完成后，可以停止守护进程：

```bash
# 使用PID文件找到进程ID并发送SIGTERM信号
kill -15 $(cat /tmp/daemon_process.pid)
```

验证进程已停止：

```bash
ps -p $(cat /tmp/daemon_process.pid)
```

应该看到类似以下输出：

```
  PID TTY          TIME CMD
```

或者提示找不到进程。

## 7. 常见问题排查

### 7.1 守护进程无法启动

检查以下几点：
- 确保配置文件路径正确
- 检查日志文件中的错误信息
- 确保端口未被占用：`netstat -tulpn | grep 9000`

### 7.2 YOLOv8配置问题

如果YOLOv8推理失败，检查以下几点：
- 确保引擎文件和配置文件路径正确
- 检查YOLOv8日志文件中的错误信息：`cat yolov8_detection.log_gpu*.txt`
- 确保GPU驱动和CUDA版本兼容

### 7.3 处理任务失败

检查以下几点：
- 确保输入目录包含图片文件
- 确保输出目录可写
- 检查工作进程日志文件中的错误信息

## 8. 自动化测试脚本

以下是一个简单的自动化测试脚本，可以用来测试守护进程和客户端：

```bash
#!/bin/bash

# 启动守护进程
echo "启动守护进程..."
cd build
./daemon_process --port 9000 --config ../config.json --daemon false &
DAEMON_PID=$!

# 等待守护进程启动
sleep 2

# 检查守护进程是否在运行
if ps -p $DAEMON_PID > /dev/null; then
    echo "守护进程已启动，PID: $DAEMON_PID"
else
    echo "守护进程启动失败"
    exit 1
fi

# 发送处理请求
echo "发送处理请求..."
cd ..
./client_test.sh

# 等待处理完成
echo "等待处理完成..."
sleep 30

# 检查结果
echo "检查处理结果..."
ls -la test_results/T-L | head -n 10
ls -la test_results/T-M | head -n 10
ls -la test_results/T-R | head -n 10

# 停止守护进程
echo "停止守护进程..."
kill -15 $DAEMON_PID
sleep 2

# 检查守护进程是否已停止
if ps -p $DAEMON_PID > /dev/null; then
    echo "守护进程未能正常停止，强制终止"
    kill -9 $DAEMON_PID
else
    echo "守护进程已正常停止"
fi

echo "测试完成"
```

将此脚本保存为 `test_daemon.sh`，然后赋予执行权限并运行：

```bash
chmod +x test_daemon.sh
./test_daemon.sh
```

## 9. 总结

通过以上测试步骤，可以验证守护进程的以下功能：
1. 守护进程化：能够在后台运行
2. 网络通信：能够接收客户端请求
3. 多GPU支持：能够利用多个GPU并行处理任务
4. YOLOv8集成：能够使用YOLOv8进行目标检测
5. 任务处理：能够处理图像并生成JSON结果文件
6. 优雅关闭：能够正常响应终止信号

这些测试确保了守护进程的稳定性和功能完整性。