{"process": {"batch_size": 1000}, "paths": {"model_config_dir": "/home/<USER>/workspace/maxi/model_config.json", "input_dir": "/home/<USER>/workspace/maxi/test_large_imgs", "output_dir": "/home/<USER>/workspace/maxi/test_results", "log_file": "multi_process_framework.log"}, "gpu": {"gpu_ids": [0, 1]}, "windows_reporter": {"enabled": true, "host": "127.0.0.1", "port": 8080, "report_interval_ms": 1000}, "yolo": {"engine_file": "/home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.transd.engine", "config_file": "/home/<USER>/workspace/maxi/model_config.json", "task_name": "yolov8_detection", "log_file": "yolov8_detection.log"}}