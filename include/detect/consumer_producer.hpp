#ifndef __CPM_HPP__
#define __CPM_HPP__

// Comsumer Producer Model

#ifdef linux
#include <pthread.h>
#endif

#include <algorithm>
#include <condition_variable>
#include <future>
#include <memory>
#include <queue>
#include <thread>
#include <chrono>

#include <cuda_runtime.h>
#include "timer.hpp"
#include "spdlog.hpp"

namespace cpm {

struct ImageIn {
    ImageIn(const cv::Mat &image, const std::string &name):
        image(image), image_path(name) {}

    cv::Mat image;
    const std::string image_path;
};

struct CpmImage {
    CpmImage(const cv::Mat &in_img,
             const std::string image_path) :
        img(in_img), image_path(image_path) {}

    std::string image_path;
    const cv::Mat img;
};

template <typename Result, typename Input, typename ForwardIn, typename Model>
class Instance {
protected:
    struct Out {
        Out() = default;
        explicit Out(Result &out,
                     const std::string name,
                     const std::string path):
            output(out), image_name(name), image_path(path) {}

        explicit Out(Result &&out):output(out) {}

        Result output;
        std::string image_name;
        std::string image_path;
    };

    struct Item {
        Item() = default;
        Item(Input &in): input(in) {}
        Item(Input &&in): input(in) {}

        Input input;
        std::shared_ptr<std::promise<Out>> pro;
    };

    std::queue<Item> input_queue_;
    std::shared_ptr<std::thread> worker_;

    std::mutex queue_lock_;
    std::condition_variable cond_;

    //volatile bool run_ = false;
    //volatile int max_items_processed_ = 0;
    bool run_ = false;
    bool need_timer = false;
    int micro_sec_laytency = 500;
    int max_items_processed_ = 0;
    int images_in_queue_ = 0;
    bool _visualize_affine=false;

    int max_queue_imgs;
    cudaStream_t stream_;
    trt::Timer timer;

private:
    bool __debug_log, __err_log;
    std::shared_ptr<spdlog::async_logger> __logger;

public:
    Instance() :
        run_(false),
        need_timer(false),
        micro_sec_laytency(500),
        max_items_processed_(0),
        images_in_queue_(0),
        _visualize_affine(false),
        max_queue_imgs(0),
        stream_(nullptr),
        __debug_log(false),
        __err_log(false) {}

    virtual ~Instance() {
        stop();
    }

    void stop() {
        run_ = false;
        cond_.notify_one();
        {
            std::unique_lock<std::mutex> l(queue_lock_);
            while (!input_queue_.empty()) {
              auto &item = input_queue_.front();
              if (item.pro) item.pro->set_value(Out(Result()));
              input_queue_.pop();
            }
        };

        if (worker_) {
            worker_->join();
            worker_.reset();
        }
    }

    virtual bool put_items_and_wait(std::vector<std::shared_future<Out>> &output,
                                    std::queue<cv::Mat> &images) {
        {
            std::unique_lock<std::mutex> l(queue_lock_);
            cond_.wait(l, [&]() { return !run_ || input_queue_.size() < max_queue_imgs;});

            if (!run_) return false;

            int diff = max_queue_imgs - input_queue_.size();
            for (int i = 0; i < diff && !images.empty(); ++i) {

                auto mat = images.front();
                if(__debug_log)
                    LOGGER_INFO(__logger, "accept one image, width={0}, height={1}",
                                mat.cols, mat.rows);

                // avoid emtpy image
                if(mat.empty()) {
                    mat.data = nullptr;
                    mat.rows = 0;
                    mat.cols= 0;
                }

                images.pop();

                Item item(Input(mat, ""));

                item.pro.reset(new std::promise<Out>());
                output.emplace_back(item.pro->get_future());
                input_queue_.push(item);

                images_in_queue_ = input_queue_.size();
            }
        } // lock

        cond_.notify_one();
        /*
            NOTE: latency here
            this will not affect efficiency
            just make sure worker thread can definitly get the lock
        */
        std::this_thread::sleep_for(std::chrono::microseconds(this->micro_sec_laytency));
        return true;
    }

    virtual bool put_items_and_wait(std::vector<std::shared_future<Out>> &output,
                                    std::queue<std::string> &files) {
        {
            std::unique_lock<std::mutex> l(queue_lock_);
            cond_.wait(l, [&]() { return !run_ || input_queue_.size() < max_queue_imgs;});

            if (!run_) return false;

            int diff = max_queue_imgs - input_queue_.size();
            for (int i = 0; i < diff && !files.empty(); ++i) {

                auto path = files.front();
                cv::Mat mat = cv::imread(path);
                // avoid emtpy image
                if(mat.empty()) {
                    mat.data = nullptr;
                    mat.rows = 0;
                    mat.cols= 0;
                }

                if(__debug_log)
                    LOGGER_INFO(__logger, "accept one image, width={0}, height={1}",
                                mat.cols, mat.rows);

                files.pop();

                Item item(Input(mat, path));

                item.pro.reset(new std::promise<Out>());
                output.emplace_back(item.pro->get_future());
                input_queue_.push(item);

                images_in_queue_ = input_queue_.size();
            }
        } // lock

        cond_.notify_one();
        /*
            NOTE: latency here
            this will not affect efficiency
            just make sure worker thread can definitly get the lock
        */
        std::this_thread::sleep_for(std::chrono::microseconds(this->micro_sec_laytency));
        return true;
    }

    virtual std::vector<std::shared_future<Out>> commits(std::queue<cv::Mat> &images) {

        std::vector<std::shared_future<Out>> output;

        while(!images.empty()) {
            put_items_and_wait(output, images);
        }

        /* actually, the compiler will add this move statement
           to make it more efficient, so we do not need to write
           move explicitly */
        return std::move(output);
    }

    virtual std::vector<std::shared_future<Out>> commits(std::queue<std::string> &files) {

        std::vector<std::shared_future<Out>> output;

        while(!files.empty()) {
            put_items_and_wait(output, files);
        }

        /* actually, the compiler will add this move statement
           to make it more efficient, so we do not need to write
           move explicitly */
        return std::move(output);
    }

    template <typename LoadMethod>
    bool start(const LoadMethod &loadmethod,  // LoadMethod: YoloInfer
               cudaStream_t &stream,
               std::shared_ptr<spdlog::async_logger> logger,
               bool debug_log,
               bool err_log,
               bool need_timer = false,
               int micro_sec_laytency=500,
               int max_items_processed = 1,
               int max_queue_imgs = 160) {
        stop();

        // 确保流是有效的
        if (stream == nullptr) {
            if (debug_log) {
                LOGGER_ERROR(logger, "Invalid CUDA stream provided to Instance::start");
            }
            return false;
        }

        // 获取当前设备
        int current_device;
        cudaGetDevice(&current_device);

        this->stream_ = stream;
        this->max_items_processed_ = max_items_processed;
        this->max_queue_imgs = max_queue_imgs;
        this->micro_sec_laytency = micro_sec_laytency;
        // 现在我们使用CPU计时器，可以安全地启用计时功能
        this->need_timer = need_timer;
        if (need_timer && debug_log) {
            LOGGER_INFO(logger, "使用CPU计时器测量推理时间");
        }
        this->__logger = logger;
        this->__debug_log = debug_log;
        this->__err_log = err_log;

        std::promise<bool> status;
        worker_ = std::make_shared<std::thread>(&Instance::worker<LoadMethod>,
                                                this,
                                                std::ref(loadmethod),
                                                std::ref(status));

        return status.get_future().get();
    }

private:
    template <typename LoadMethod>
    void worker(const LoadMethod &loadmethod, std::promise<bool> &status) {
        // 获取当前设备
        int device_id = -1;
        cudaGetDevice(&device_id);

        if (__debug_log) {
            LOGGER_INFO(__logger, "Worker thread starting on device {0}", device_id);
        }

        // 确保在正确的设备上下文中运行
        cudaSetDevice(device_id);

        if (__debug_log) {
            LOGGER_INFO(__logger, "Worker thread set to device {0}", device_id);
        }

        std::shared_ptr<Model> model = loadmethod;
        if (model == nullptr) {
            status.set_value(false);
            return;
        }

        run_ = true;
        status.set_value(true);

        std::vector<Item> fetch_items;
        int img_in_queue = 0;
        while (get_items_and_wait(fetch_items, max_items_processed_, img_in_queue)) {

            int blocks = int((img_in_queue + max_items_processed_ -1)/max_items_processed_);

            int left_imgs = img_in_queue;
            int img_in_block = 0;

            auto item_itr = fetch_items.begin();
            std::vector<ForwardIn> inputs;

            for(int i = 0; i<blocks; ++i) {

                if(left_imgs == 0) break;

                img_in_block = max_items_processed_;
                if(left_imgs < max_items_processed_) {
                    img_in_block = left_imgs;
                }
                else {
                    left_imgs = img_in_queue - img_in_block;
                }

                img_in_queue -= img_in_block;

                inputs.resize(img_in_block);

                std::transform(item_itr,
                               item_itr+img_in_block,
                               inputs.begin(),
                               [](Item &item) {
                                   return ForwardIn(item.input.img.data,
                                                    item.input.img.cols,
                                                    item.input.img.rows,
                                                    item.input.image_path);
                               });

                // 获取当前设备
                int current_device = -1;
                cudaGetDevice(&current_device);

                if (__debug_log) {
                    LOGGER_INFO(__logger, "Before inference, current device: {0}", current_device);
                }

                // 使用CPU计时器来测量推理时间，避免CUDA上下文问题
                auto start_time = std::chrono::high_resolution_clock::now();

                // 记录开始时间和图片信息，用于后续打印
                std::vector<std::string> image_paths;
                for (const auto& input : inputs) {
                    image_paths.push_back(input.image_path);
                }

                if (this->need_timer) {
                    printf("开始推理 %d 张图片\n", img_in_block);
                }

                auto ret = model->forwards(inputs, stream_);

                // 再次获取当前设备，确认是否发生了变化
                int after_device = -1;
                cudaGetDevice(&after_device);

                if (__debug_log) {
                    LOGGER_INFO(__logger, "After inference, current device: {0}", after_device);
                }

                // 确保在正确的设备上下文中
                if (current_device != after_device) {
                    cudaSetDevice(current_device);
                    if (__debug_log) {
                        LOGGER_INFO(__logger, "Restored device to {0} after inference", current_device);
                    }
                }

                if (__debug_log) {
                    LOGGER_INFO(__logger, "Inference completed on device {0}", current_device);
                }

                // 计算推理时间
                if (this->need_timer) {
                    auto end_time = std::chrono::high_resolution_clock::now();
                    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();

                    // 打印总推理时间
                    printf("推理完成，总耗时: %lld 毫秒，平均每张图片: %.2f 毫秒\n",
                           duration, static_cast<float>(duration) / img_in_block);

                    // 打印每张图片的信息
                    for (int i = 0; i < img_in_block && i < image_paths.size(); ++i) {
                        std::string image_name = get_image_name(image_paths[i]);
                        printf("图片 %s 推理完成\n", image_name.c_str());
                    }
                }

                for (int i = 0; i < img_in_block; ++i) {
                    if (i < (int)ret.size()) {
                        auto image_name = get_image_name(inputs[i].image_path);
                        (*(item_itr+i)).pro->set_value(Out(ret[i], image_name,
                                                          inputs[i].image_path));
                        if(__debug_log)
                            LOGGER_INFO(__logger, "image_name={0}, get result", image_name);
                    } else {
                        (*(item_itr+i)).pro->set_value(Out(Result()));
                    }
                }

                item_itr += img_in_block;
                inputs.clear();
            }

            fetch_items.clear();
        }

        model.reset();
        run_ = false;
    }

    virtual bool get_items_and_wait(std::vector<Item> &fetch_items,
                                    int max_size,
                                    int &img_in_queue) {
        {
            std::unique_lock<std::mutex> l(queue_lock_);
            cond_.wait(l, [&]() { return !run_ || !input_queue_.empty(); });

            if (!run_) return false;
            fetch_items.clear();
            img_in_queue = images_in_queue_;
            for (int i = 0; i < images_in_queue_ && !input_queue_.empty(); ++i) {
                fetch_items.emplace_back(std::move(input_queue_.front()));
                input_queue_.pop();

            }

        }

        cond_.notify_one();
        return true;
    }

    virtual std::string get_image_name(const std::string &path) {
        // '/' for linux ; '\\' for windows
        size_t position = path.find_last_of("/\\");

        // get the last split string
        return path.substr(position+1, path.length()-position-1);
    }

    /*
    void setThreadPriority(std::thread& thread, int priority, int policy) {
        pthread_t threadId = static_cast<pthread_t>(thread.native_handle());
        //int policy = SCHED_FIFO; // or SCHED_RR, SCHED_OTHER
        struct sched_param param;
        param.sched_priority = priority;

        int result = pthread_setschedparam(threadId, policy, &param);

        if (result != 0) {
            std::cerr << "Failed to set thread priority." << std::endl;
        }
    }
    */

};

} // namespace cpm

#endif  // __CPM_HPP__
