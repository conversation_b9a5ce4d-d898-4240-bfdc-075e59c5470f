#ifndef __SESSION_HPP__
#define __SESSION_HPP__

#include <string>
#include <vector>

/* task */
typedef struct Task {
    std::vector<std::string> classes;
    int num_classes;
    int debug_log;
    int err_log;
} Task_s;

/* preprocess */
typedef struct Preprocess {
    int visualize_affine;
    int show_info;
    int max_batch_size;
} Pre_s;

/* postprocess */
typedef struct Postprocess {
    float nms_threshold;
    float confidence_threshold;
    int num_box_element;
    int max_image_bboxes;
} Post_s;

/* GPU */
typedef struct GPU {
    int gpu_block_threads;
    int max_block_size;
    int mem_align;
    std::vector<int> gpu_id;
} GPU_s;

/* cpu multi_threads */
typedef struct CpuMt{
    int timer;
    int max_queue_imgs;
    int micro_sec_laytency;
}CpuMt_s;

#endif // __SESSION_HPP__
