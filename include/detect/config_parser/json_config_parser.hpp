#ifndef JSON_CONFIG_PARSER_HPP
#define JSON_CONFIG_PARSER_HPP

#include <string>
#include <vector>
#include <fstream>
#include <iostream>
#include <sstream>
#include <nlohmann/json.hpp>

// 使用 nlohmann/json 库
using json = nlohmann::json;

// 任务配置结构体
struct TaskConfig {
    std::vector<std::string> classes;
    int num_classes;
    int debug_log;
    int err_log;
};

// 预处理配置结构体
struct PreprocessConfig {
    int visualize_affine;
    int show_info;
    int max_batch_size;
};

// 后处理配置结构体
struct PostprocessConfig {
    float confidence_threshold;
    float nms_threshold;
    int num_box_element;
    int max_image_bboxes;
};

// GPU 配置结构体
struct GPUConfig {
    int gpu_block_threads;
    int max_block_size;
    int mem_align;
    std::vector<int> gpu_id;
};

// CPU 多线程配置结构体
struct CPUMultiThreadConfig {
    int timer;
    int max_queue_imgs;
    int micro_sec_laytency;
};

// 完整配置结构体
struct YoloConfig {
    TaskConfig task;
    PreprocessConfig preprocess;
    PostprocessConfig postprocess;
    GPUConfig gpu;
    CPUMultiThreadConfig multi_threads;
};

class JsonConfigParser {
public:
    JsonConfigParser() = default;
    ~JsonConfigParser() = default;

    // 从文件加载配置
    bool loadConfig(const std::string& filename) {
        try {
            std::ifstream file(filename);
            if (!file.is_open()) {
                std::cerr << "Could not open config file: " << filename << std::endl;
                return false;
            }

            file >> config_json;
            file.close();

            // 解析配置
            parseConfig();
            return true;
        } catch (const std::exception& e) {
            std::cerr << "Error parsing JSON config: " << e.what() << std::endl;
            return false;
        }
    }

    // 获取配置
    const YoloConfig& getConfig() const {
        return config;
    }

private:
    // 解析配置
    void parseConfig() {
        // 解析任务配置
        if (config_json.contains("task")) {
            auto& task_json = config_json["task"];

            // 解析类别
            if (task_json.contains("classes")) {
                if (task_json["classes"].is_array()) {
                    config.task.classes = task_json["classes"].get<std::vector<std::string>>();
                } else if (task_json["classes"].is_string()) {
                    // 处理字符串形式的类别列表
                    std::string classes_str = task_json["classes"];
                    std::stringstream ss(classes_str);
                    std::string item;
                    while (std::getline(ss, item, ',')) {
                        // 去除前后空格
                        item.erase(0, item.find_first_not_of(" "));
                        item.erase(item.find_last_not_of(" ") + 1);
                        config.task.classes.push_back(item);
                    }
                }
            }

            config.task.num_classes = config.task.classes.size();
            config.task.debug_log = task_json.value("debug_log", 0);
            config.task.err_log = task_json.value("err_log", 0);
        }

        // 解析预处理配置
        if (config_json.contains("preprocess")) {
            auto& preprocess_json = config_json["preprocess"];
            config.preprocess.visualize_affine = preprocess_json.value("visualize_affine", 0);
            config.preprocess.show_info = preprocess_json.value("show_info", 0);
            config.preprocess.max_batch_size = preprocess_json.value("max_batch_size", 16);
        }

        // 解析后处理配置
        if (config_json.contains("postprocess")) {
            auto& postprocess_json = config_json["postprocess"];
            config.postprocess.confidence_threshold = postprocess_json.value("confidence_threshold", 0.25f);
            config.postprocess.nms_threshold = postprocess_json.value("nms_threshold", 0.5f);
            config.postprocess.num_box_element = postprocess_json.value("num_box_element", 8);
            config.postprocess.max_image_bboxes = postprocess_json.value("max_image_bboxes", 128);
        }

        // 解析 GPU 配置
        if (config_json.contains("gpu")) {
            auto& gpu_json = config_json["gpu"];
            config.gpu.gpu_block_threads = gpu_json.value("gpu_block_threads", 512);
            config.gpu.max_block_size = gpu_json.value("max_block_size", 32);
            config.gpu.mem_align = gpu_json.value("mem_align", 32);

            // 解析 GPU ID
            if (gpu_json.contains("gpu_id")) {
                if (gpu_json["gpu_id"].is_array()) {
                    config.gpu.gpu_id = gpu_json["gpu_id"].get<std::vector<int>>();
                } else if (gpu_json["gpu_id"].is_number()) {
                    config.gpu.gpu_id.push_back(gpu_json["gpu_id"].get<int>());
                } else if (gpu_json["gpu_id"].is_string()) {
                    // 处理字符串形式的 GPU ID 列表
                    std::string gpu_id_str = gpu_json["gpu_id"];
                    std::stringstream ss(gpu_id_str);
                    std::string item;
                    while (std::getline(ss, item, ',')) {
                        config.gpu.gpu_id.push_back(std::stoi(item));
                    }
                }
            } else {
                // 默认使用 GPU 0
                config.gpu.gpu_id.push_back(0);
            }
        }

        // 解析 CPU 多线程配置
        if (config_json.contains("multi_threads")) {
            auto& mt_json = config_json["multi_threads"];
            config.multi_threads.timer = mt_json.value("timer", 0);
            config.multi_threads.max_queue_imgs = mt_json.value("max_queue_imgs", 160);
            config.multi_threads.micro_sec_laytency = mt_json.value("micro_sec_laytency", 500);
        }
    }

    json config_json;
    YoloConfig config;
};

#endif // JSON_CONFIG_PARSER_HPP
