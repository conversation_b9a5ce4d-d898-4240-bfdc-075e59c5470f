#ifndef MPF_DAEMON_PROCESS_H
#define MPF_DAEMON_PROCESS_H

#include <string>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <nlohmann/json.hpp>
#include "main_process.h"

namespace mpf {

/**
 * @brief 守护进程类，负责在后台运行并接收客户端请求
 */
class DaemonProcess {
public:
    /**
     * @brief 构造函数
     *
     * @param listenPort 监听端口
     * @param configFile 配置文件路径
     */
    DaemonProcess(int listenPort, const std::string& configFile);

    /**
     * @brief 析构函数
     */
    ~DaemonProcess();

    /**
     * @brief 启动守护进程
     *
     * @return true 启动成功
     * @return false 启动失败
     */
    bool start();

    /**
     * @brief 停止守护进程
     */
    void stop();

    /**
     * @brief 运行守护进程
     *
     * @return int 返回码
     */
    int run();

private:
    /**
     * @brief 守护进程化
     *
     * @return true 成功
     * @return false 失败
     */
    bool daemonize();

    /**
     * @brief 监听线程函数
     */
    void listenThread();

    /**
     * @brief 处理客户端连接
     *
     * @param clientSocket 客户端套接字
     */
    void handleClient(int clientSocket);

    /**
     * @brief 处理客户端请求
     *
     * @param request 请求JSON
     * @param clientSocket 客户端套接字
     */
    void processRequest(const nlohmann::json& request, int clientSocket);

    /**
     * @brief 启动图片处理任务
     *
     * @param inputDir 输入目录
     * @param outputDir 输出目录
     * @param clientSocket 客户端套接字
     * @param gpuIds 客户端指定的GPU ID列表
     * @return true 启动成功
     * @return false 启动失败
     */
    bool startImageProcessing(const std::string& inputDir, const std::string& outputDir, int clientSocket, const std::vector<int>& gpuIds = std::vector<int>());

    /**
     * @brief 发送响应给客户端
     *
     * @param clientSocket 客户端套接字
     * @param success 是否成功
     * @param message 消息
     * @return true 发送成功
     * @return false 发送失败
     */
    bool sendResponse(int clientSocket, bool success, const std::string& message);

    /**
     * @brief 发送进度更新给客户端
     *
     * @param clientSocket 客户端套接字
     * @param processed 已处理数量
     * @param total 总数量
     * @return true 发送成功
     * @return false 发送失败
     */
    bool sendProgressUpdate(int clientSocket, int processed, int total);

    /**
     * @brief 发送完成通知给客户端
     *
     * @param clientSocket 客户端套接字
     * @param success 是否成功
     * @param message 消息
     * @return true 发送成功
     * @return false 发送失败
     */
    bool sendCompletionNotice(int clientSocket, bool success, const std::string& message);

    int listenPort_;                  ///< 监听端口
    std::string configFile_;          ///< 配置文件路径
    std::atomic<bool> running_;       ///< 运行标志
    std::thread listenThread_;        ///< 监听线程
    std::mutex mutex_;                ///< 互斥锁
    std::condition_variable cv_;      ///< 条件变量

    // 配置参数
    int numProcesses_;                ///< 进程数量
    std::string defaultInputDir_;     ///< 默认输入目录
    std::string defaultOutputDir_;    ///< 默认输出目录
    std::string logFile_;             ///< 日志文件
    int batchSize_;                   ///< 批处理大小

    // 当前处理任务
    std::atomic<bool> processing_;    ///< 是否正在处理
    int currentClientSocket_;         ///< 当前客户端套接字
    std::unique_ptr<MainProcess> mainProcess_; ///< 主处理进程
};

} // namespace mpf

#endif // MPF_DAEMON_PROCESS_H
