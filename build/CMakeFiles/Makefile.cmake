# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/workspace/maxi/CMakeLists.txt"
  "CMakeFiles/3.27.9/CMakeCUDACompiler.cmake"
  "CMakeFiles/3.27.9/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.27.9/CMakeSystem.cmake"
  "/usr/lib/cmake/nlohmann_json/nlohmann_jsonConfig.cmake"
  "/usr/lib/cmake/nlohmann_json/nlohmann_jsonConfigVersion.cmake"
  "/usr/lib/cmake/nlohmann_json/nlohmann_jsonTargets.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Boost-1.71.0/BoostConfig.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/Boost-1.71.0/BoostConfigVersion.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/BoostDetectToolset-1.71.0.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_filesystem-1.71.0/boost_filesystem-config-version.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_filesystem-1.71.0/boost_filesystem-config.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_filesystem-1.71.0/libboost_filesystem-variant-shared.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_filesystem-1.71.0/libboost_filesystem-variant-static.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_headers-1.71.0/boost_headers-config-version.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_headers-1.71.0/boost_headers-config.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_program_options-1.71.0/boost_program_options-config-version.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_program_options-1.71.0/boost_program_options-config.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_program_options-1.71.0/libboost_program_options-variant-shared.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_program_options-1.71.0/libboost_program_options-variant-static.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_system-1.71.0/boost_system-config-version.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_system-1.71.0/boost_system-config.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_system-1.71.0/libboost_system-variant-shared.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/boost_system-1.71.0/libboost_system-variant-static.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/opencv4/OpenCVConfig-version.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/opencv4/OpenCVConfig.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/opencv4/OpenCVModules-release.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/opencv4/OpenCVModules.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/spdlog/spdlogConfig.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/spdlog/spdlogConfigTargets-none.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/spdlog/spdlogConfigTargets.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/spdlog/spdlogConfigVersion.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeCUDACompiler.cmake.in"
  "/usr/share/cmake-3.27/Modules/CMakeCUDACompilerABI.cu"
  "/usr/share/cmake-3.27/Modules/CMakeCUDAInformation.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeCXXCompiler.cmake.in"
  "/usr/share/cmake-3.27/Modules/CMakeCXXCompilerABI.cpp"
  "/usr/share/cmake-3.27/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeDetermineCUDACompiler.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeDetermineCXXCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeDetermineCompileFeatures.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeDetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeDetermineSystem.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeFindBinUtils.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeParseLibraryArchitecture.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeSystem.cmake.in"
  "/usr/share/cmake-3.27/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeTestCUDACompiler.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeTestCXXCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/share/cmake-3.27/Modules/CMakeUnixFindMake.cmake"
  "/usr/share/cmake-3.27/Modules/CUDA/architectures.cmake"
  "/usr/share/cmake-3.27/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/share/cmake-3.27/Modules/CheckIncludeFileCXX.cmake"
  "/usr/share/cmake-3.27/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/LCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/NVIDIA-CUDA.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.27/Modules/FindBoost.cmake"
  "/usr/share/cmake-3.27/Modules/FindCUDA.cmake"
  "/usr/share/cmake-3.27/Modules/FindCUDA/select_compute_arch.cmake"
  "/usr/share/cmake-3.27/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.27/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.27/Modules/FindPkgConfig.cmake"
  "/usr/share/cmake-3.27/Modules/FindThreads.cmake"
  "/usr/share/cmake-3.27/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/share/cmake-3.27/Modules/Internal/FeatureTesting.cmake"
  "/usr/share/cmake-3.27/Modules/Platform/Linux-Determine-CXX.cmake"
  "/usr/share/cmake-3.27/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.27/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.27/Modules/Platform/Linux-Initialize.cmake"
  "/usr/share/cmake-3.27/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.27/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.27.9/CMakeSystem.cmake"
  "CMakeFiles/3.27.9/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.27.9/CMakeCUDACompiler.cmake"
  "CMakeFiles/3.27.9/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.27.9/CMakeCUDACompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/utils.dir/DependInfo.cmake"
  "CMakeFiles/yolo_infer.dir/DependInfo.cmake"
  "CMakeFiles/mpf.dir/DependInfo.cmake"
  "CMakeFiles/multi_process_framework.dir/DependInfo.cmake"
  "CMakeFiles/worker_process.dir/DependInfo.cmake"
  "CMakeFiles/daemon_process.dir/DependInfo.cmake"
  "CMakeFiles/yolov8-multi-gpu.dir/DependInfo.cmake"
  )
