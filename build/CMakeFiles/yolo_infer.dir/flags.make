# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# compile CU<PERSON> with /usr/local/cuda-11.8/bin/nvcc
# compile CXX with /usr/bin/c++
CUDA_DEFINES = -DSPDLOG_COMPILED_LIB -Dyolo_infer_EXPORTS

CUDA_INCLUDES = --options-file CMakeFiles/yolo_infer.dir/includes_CUDA.rsp

CUDA_FLAGS = -g -std=c++17 "--generate-code=arch=compute_86,code=[compute_86,sm_86]" -Xcompiler=-fPIC

CXX_DEFINES = -DSPDLOG_COMPILED_LIB -Dyolo_infer_EXPORTS

CXX_INCLUDES = -I/home/<USER>/workspace/maxi/include -I/usr/local/cuda-11.8/include -I/usr/include/pgm-5.2 -I/usr/include/mit-krb5 -isystem /usr/include/opencv4

CXX_FLAGS = -g -std=gnu++17 -fPIC

