file(REMOVE_RECURSE
  "CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.o"
  "CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.o.d"
  "CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.o"
  "CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.o.d"
  "CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.o"
  "CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.o.d"
  "CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.o"
  "CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.o.d"
  "CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.o"
  "CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.o.d"
  "CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.o"
  "CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.o.d"
  "CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.o"
  "CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.o.d"
  "CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.o"
  "CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.o.d"
  "CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.o"
  "CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.o.d"
  "CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.o"
  "CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.o.d"
  "CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.o"
  "CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.o.d"
  "CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.o"
  "CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.o.d"
  "CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.o"
  "CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.o.d"
  "libyolo_infer.pdb"
  "libyolo_infer.so"
)

# Per-language clean rules from dependency scanning.
foreach(lang CUDA CXX)
  include(CMakeFiles/yolo_infer.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
