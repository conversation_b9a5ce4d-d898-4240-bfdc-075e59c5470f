# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/workspace/maxi

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/workspace/maxi/build

# Include any dependencies generated for this target.
include CMakeFiles/yolo_infer.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/yolo_infer.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/yolo_infer.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/yolo_infer.dir/flags.make

CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.o: CMakeFiles/yolo_infer.dir/flags.make
CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.o: /home/<USER>/workspace/maxi/lib/detect/common.cpp
CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.o: CMakeFiles/yolo_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.o -MF CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.o.d -o CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.o -c /home/<USER>/workspace/maxi/lib/detect/common.cpp

CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/detect/common.cpp > CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.i

CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/detect/common.cpp -o CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.s

CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.o: CMakeFiles/yolo_infer.dir/flags.make
CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.o: CMakeFiles/yolo_infer.dir/includes_CUDA.rsp
CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.o: /home/<USER>/workspace/maxi/lib/detect/infer.cu
CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.o: CMakeFiles/yolo_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CUDA object CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.o"
	/usr/local/cuda-11.8/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.o -MF CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.o.d -x cu -c /home/<USER>/workspace/maxi/lib/detect/infer.cu -o CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.o

CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CUDA source to CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CUDA source to assembly CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.o: CMakeFiles/yolo_infer.dir/flags.make
CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.o: /home/<USER>/workspace/maxi/lib/detect/log.cpp
CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.o: CMakeFiles/yolo_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.o -MF CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.o.d -o CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.o -c /home/<USER>/workspace/maxi/lib/detect/log.cpp

CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/detect/log.cpp > CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.i

CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/detect/log.cpp -o CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.s

CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.o: CMakeFiles/yolo_infer.dir/flags.make
CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.o: /home/<USER>/workspace/maxi/lib/detect/memory.cpp
CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.o: CMakeFiles/yolo_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.o -MF CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.o.d -o CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.o -c /home/<USER>/workspace/maxi/lib/detect/memory.cpp

CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/detect/memory.cpp > CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.i

CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/detect/memory.cpp -o CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.s

CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.o: CMakeFiles/yolo_infer.dir/flags.make
CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.o: /home/<USER>/workspace/maxi/lib/detect/parse_config.cpp
CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.o: CMakeFiles/yolo_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.o -MF CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.o.d -o CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.o -c /home/<USER>/workspace/maxi/lib/detect/parse_config.cpp

CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/detect/parse_config.cpp > CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.i

CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/detect/parse_config.cpp -o CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.s

CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.o: CMakeFiles/yolo_infer.dir/flags.make
CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.o: CMakeFiles/yolo_infer.dir/includes_CUDA.rsp
CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.o: /home/<USER>/workspace/maxi/lib/detect/postprocess.cu
CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.o: CMakeFiles/yolo_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CUDA object CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.o"
	/usr/local/cuda-11.8/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.o -MF CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.o.d -x cu -c /home/<USER>/workspace/maxi/lib/detect/postprocess.cu -o CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.o

CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CUDA source to CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CUDA source to assembly CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.o: CMakeFiles/yolo_infer.dir/flags.make
CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.o: CMakeFiles/yolo_infer.dir/includes_CUDA.rsp
CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.o: /home/<USER>/workspace/maxi/lib/detect/preprocess.cu
CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.o: CMakeFiles/yolo_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CUDA object CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.o"
	/usr/local/cuda-11.8/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.o -MF CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.o.d -x cu -c /home/<USER>/workspace/maxi/lib/detect/preprocess.cu -o CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.o

CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CUDA source to CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CUDA source to assembly CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.o: CMakeFiles/yolo_infer.dir/flags.make
CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.o: /home/<USER>/workspace/maxi/lib/detect/spdlog.cpp
CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.o: CMakeFiles/yolo_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.o -MF CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.o.d -o CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.o -c /home/<USER>/workspace/maxi/lib/detect/spdlog.cpp

CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/detect/spdlog.cpp > CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.i

CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/detect/spdlog.cpp -o CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.s

CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.o: CMakeFiles/yolo_infer.dir/flags.make
CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.o: /home/<USER>/workspace/maxi/lib/detect/test.cpp
CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.o: CMakeFiles/yolo_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.o -MF CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.o.d -o CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.o -c /home/<USER>/workspace/maxi/lib/detect/test.cpp

CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/detect/test.cpp > CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.i

CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/detect/test.cpp -o CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.s

CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.o: CMakeFiles/yolo_infer.dir/flags.make
CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.o: /home/<USER>/workspace/maxi/lib/detect/timer.cpp
CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.o: CMakeFiles/yolo_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.o -MF CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.o.d -o CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.o -c /home/<USER>/workspace/maxi/lib/detect/timer.cpp

CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/detect/timer.cpp > CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.i

CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/detect/timer.cpp -o CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.s

CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.o: CMakeFiles/yolo_infer.dir/flags.make
CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.o: /home/<USER>/workspace/maxi/lib/detect/util.cpp
CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.o: CMakeFiles/yolo_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.o -MF CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.o.d -o CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.o -c /home/<USER>/workspace/maxi/lib/detect/util.cpp

CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/detect/util.cpp > CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.i

CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/detect/util.cpp -o CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.s

CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.o: CMakeFiles/yolo_infer.dir/flags.make
CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.o: /home/<USER>/workspace/maxi/lib/detect/yolo.cpp
CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.o: CMakeFiles/yolo_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.o -MF CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.o.d -o CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.o -c /home/<USER>/workspace/maxi/lib/detect/yolo.cpp

CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/detect/yolo.cpp > CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.i

CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/detect/yolo.cpp -o CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.s

CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.o: CMakeFiles/yolo_infer.dir/flags.make
CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.o: /home/<USER>/workspace/maxi/lib/detect/yolo_infer.cpp
CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.o: CMakeFiles/yolo_infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.o -MF CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.o.d -o CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.o -c /home/<USER>/workspace/maxi/lib/detect/yolo_infer.cpp

CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/detect/yolo_infer.cpp > CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.i

CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/detect/yolo_infer.cpp -o CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.s

# Object files for target yolo_infer
yolo_infer_OBJECTS = \
"CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.o" \
"CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.o" \
"CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.o" \
"CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.o" \
"CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.o" \
"CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.o" \
"CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.o" \
"CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.o" \
"CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.o" \
"CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.o" \
"CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.o" \
"CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.o" \
"CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.o"

# External object files for target yolo_infer
yolo_infer_EXTERNAL_OBJECTS =

libyolo_infer.so: CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.o
libyolo_infer.so: CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.o
libyolo_infer.so: CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.o
libyolo_infer.so: CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.o
libyolo_infer.so: CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.o
libyolo_infer.so: CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.o
libyolo_infer.so: CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.o
libyolo_infer.so: CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.o
libyolo_infer.so: CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.o
libyolo_infer.so: CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.o
libyolo_infer.so: CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.o
libyolo_infer.so: CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.o
libyolo_infer.so: CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.o
libyolo_infer.so: CMakeFiles/yolo_infer.dir/build.make
libyolo_infer.so: libutils.a
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_face.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.2.0
libyolo_infer.so: /usr/local/cuda-11.8/lib64/libnvinfer.so.8
libyolo_infer.so: /usr/local/cuda-11.8/lib64/libnvinfer_plugin.so.8
libyolo_infer.so: /usr/local/cuda-11.8/lib64/libnvonnxparser.so.8
libyolo_infer.so: /usr/local/cuda-11.8/lib64/libcudart_static.a
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/librt.so
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libspdlog.so.1.5.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_text.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_video.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.2.0
libyolo_infer.so: /usr/lib/x86_64-linux-gnu/libopencv_core.so.4.2.0
libyolo_infer.so: CMakeFiles/yolo_infer.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Linking CXX shared library libyolo_infer.so"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/yolo_infer.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/yolo_infer.dir/build: libyolo_infer.so
.PHONY : CMakeFiles/yolo_infer.dir/build

CMakeFiles/yolo_infer.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/yolo_infer.dir/cmake_clean.cmake
.PHONY : CMakeFiles/yolo_infer.dir/clean

CMakeFiles/yolo_infer.dir/depend:
	cd /home/<USER>/workspace/maxi/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/workspace/maxi /home/<USER>/workspace/maxi /home/<USER>/workspace/maxi/build /home/<USER>/workspace/maxi/build /home/<USER>/workspace/maxi/build/CMakeFiles/yolo_infer.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/yolo_infer.dir/depend

