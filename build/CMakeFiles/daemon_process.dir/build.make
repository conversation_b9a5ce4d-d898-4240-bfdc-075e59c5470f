# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/workspace/maxi

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/workspace/maxi/build

# Include any dependencies generated for this target.
include CMakeFiles/daemon_process.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/daemon_process.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/daemon_process.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/daemon_process.dir/flags.make

CMakeFiles/daemon_process.dir/app/daemon.cpp.o: CMakeFiles/daemon_process.dir/flags.make
CMakeFiles/daemon_process.dir/app/daemon.cpp.o: /home/<USER>/workspace/maxi/app/daemon.cpp
CMakeFiles/daemon_process.dir/app/daemon.cpp.o: CMakeFiles/daemon_process.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/daemon_process.dir/app/daemon.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/daemon_process.dir/app/daemon.cpp.o -MF CMakeFiles/daemon_process.dir/app/daemon.cpp.o.d -o CMakeFiles/daemon_process.dir/app/daemon.cpp.o -c /home/<USER>/workspace/maxi/app/daemon.cpp

CMakeFiles/daemon_process.dir/app/daemon.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/daemon_process.dir/app/daemon.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/app/daemon.cpp > CMakeFiles/daemon_process.dir/app/daemon.cpp.i

CMakeFiles/daemon_process.dir/app/daemon.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/daemon_process.dir/app/daemon.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/app/daemon.cpp -o CMakeFiles/daemon_process.dir/app/daemon.cpp.s

# Object files for target daemon_process
daemon_process_OBJECTS = \
"CMakeFiles/daemon_process.dir/app/daemon.cpp.o"

# External object files for target daemon_process
daemon_process_EXTERNAL_OBJECTS =

daemon_process: CMakeFiles/daemon_process.dir/app/daemon.cpp.o
daemon_process: CMakeFiles/daemon_process.dir/build.make
daemon_process: libmpf.a
daemon_process: libyolo_infer.so
daemon_process: libutils.a
daemon_process: /usr/lib/x86_64-linux-gnu/libspdlog.so.1.5.0
daemon_process: /usr/local/cuda-11.8/lib64/libnvinfer.so.8
daemon_process: /usr/local/cuda-11.8/lib64/libnvinfer_plugin.so.8
daemon_process: /usr/local/cuda-11.8/lib64/libnvonnxparser.so.8
daemon_process: /usr/local/cuda-11.8/lib64/libcudart_static.a
daemon_process: /usr/lib/x86_64-linux-gnu/librt.so
daemon_process: /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0
daemon_process: /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0
daemon_process: /usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_face.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_text.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_video.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.2.0
daemon_process: /usr/lib/x86_64-linux-gnu/libopencv_core.so.4.2.0
daemon_process: CMakeFiles/daemon_process.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable daemon_process"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/daemon_process.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/daemon_process.dir/build: daemon_process
.PHONY : CMakeFiles/daemon_process.dir/build

CMakeFiles/daemon_process.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/daemon_process.dir/cmake_clean.cmake
.PHONY : CMakeFiles/daemon_process.dir/clean

CMakeFiles/daemon_process.dir/depend:
	cd /home/<USER>/workspace/maxi/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/workspace/maxi /home/<USER>/workspace/maxi /home/<USER>/workspace/maxi/build /home/<USER>/workspace/maxi/build /home/<USER>/workspace/maxi/build/CMakeFiles/daemon_process.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/daemon_process.dir/depend

