# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/workspace/maxi

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/workspace/maxi/build

# Include any dependencies generated for this target.
include CMakeFiles/mpf.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/mpf.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/mpf.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/mpf.dir/flags.make

CMakeFiles/mpf.dir/lib/mpf/message.cpp.o: CMakeFiles/mpf.dir/flags.make
CMakeFiles/mpf.dir/lib/mpf/message.cpp.o: /home/<USER>/workspace/maxi/lib/mpf/message.cpp
CMakeFiles/mpf.dir/lib/mpf/message.cpp.o: CMakeFiles/mpf.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/mpf.dir/lib/mpf/message.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mpf.dir/lib/mpf/message.cpp.o -MF CMakeFiles/mpf.dir/lib/mpf/message.cpp.o.d -o CMakeFiles/mpf.dir/lib/mpf/message.cpp.o -c /home/<USER>/workspace/maxi/lib/mpf/message.cpp

CMakeFiles/mpf.dir/lib/mpf/message.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/mpf.dir/lib/mpf/message.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/mpf/message.cpp > CMakeFiles/mpf.dir/lib/mpf/message.cpp.i

CMakeFiles/mpf.dir/lib/mpf/message.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/mpf.dir/lib/mpf/message.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/mpf/message.cpp -o CMakeFiles/mpf.dir/lib/mpf/message.cpp.s

CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.o: CMakeFiles/mpf.dir/flags.make
CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.o: /home/<USER>/workspace/maxi/lib/mpf/task_manager.cpp
CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.o: CMakeFiles/mpf.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.o -MF CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.o.d -o CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.o -c /home/<USER>/workspace/maxi/lib/mpf/task_manager.cpp

CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/mpf/task_manager.cpp > CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.i

CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/mpf/task_manager.cpp -o CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.s

CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.o: CMakeFiles/mpf.dir/flags.make
CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.o: /home/<USER>/workspace/maxi/lib/mpf/gpu_detector.cpp
CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.o: CMakeFiles/mpf.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.o -MF CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.o.d -o CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.o -c /home/<USER>/workspace/maxi/lib/mpf/gpu_detector.cpp

CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/mpf/gpu_detector.cpp > CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.i

CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/mpf/gpu_detector.cpp -o CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.s

CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.o: CMakeFiles/mpf.dir/flags.make
CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.o: /home/<USER>/workspace/maxi/lib/mpf/worker_process.cpp
CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.o: CMakeFiles/mpf.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.o -MF CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.o.d -o CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.o -c /home/<USER>/workspace/maxi/lib/mpf/worker_process.cpp

CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/mpf/worker_process.cpp > CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.i

CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/mpf/worker_process.cpp -o CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.s

CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.o: CMakeFiles/mpf.dir/flags.make
CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.o: /home/<USER>/workspace/maxi/lib/mpf/main_process.cpp
CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.o: CMakeFiles/mpf.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.o -MF CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.o.d -o CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.o -c /home/<USER>/workspace/maxi/lib/mpf/main_process.cpp

CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/mpf/main_process.cpp > CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.i

CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/mpf/main_process.cpp -o CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.s

CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.o: CMakeFiles/mpf.dir/flags.make
CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.o: /home/<USER>/workspace/maxi/lib/mpf/multi_process_framework.cpp
CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.o: CMakeFiles/mpf.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.o -MF CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.o.d -o CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.o -c /home/<USER>/workspace/maxi/lib/mpf/multi_process_framework.cpp

CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/mpf/multi_process_framework.cpp > CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.i

CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/mpf/multi_process_framework.cpp -o CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.s

CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.o: CMakeFiles/mpf.dir/flags.make
CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.o: /home/<USER>/workspace/maxi/lib/mpf/windows_reporter.cpp
CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.o: CMakeFiles/mpf.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.o -MF CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.o.d -o CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.o -c /home/<USER>/workspace/maxi/lib/mpf/windows_reporter.cpp

CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/mpf/windows_reporter.cpp > CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.i

CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/mpf/windows_reporter.cpp -o CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.s

CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.o: CMakeFiles/mpf.dir/flags.make
CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.o: /home/<USER>/workspace/maxi/lib/mpf/daemon_process.cpp
CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.o: CMakeFiles/mpf.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.o -MF CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.o.d -o CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.o -c /home/<USER>/workspace/maxi/lib/mpf/daemon_process.cpp

CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/lib/mpf/daemon_process.cpp > CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.i

CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/lib/mpf/daemon_process.cpp -o CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.s

# Object files for target mpf
mpf_OBJECTS = \
"CMakeFiles/mpf.dir/lib/mpf/message.cpp.o" \
"CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.o" \
"CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.o" \
"CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.o" \
"CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.o" \
"CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.o" \
"CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.o" \
"CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.o"

# External object files for target mpf
mpf_EXTERNAL_OBJECTS =

libmpf.a: CMakeFiles/mpf.dir/lib/mpf/message.cpp.o
libmpf.a: CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.o
libmpf.a: CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.o
libmpf.a: CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.o
libmpf.a: CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.o
libmpf.a: CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.o
libmpf.a: CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.o
libmpf.a: CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.o
libmpf.a: CMakeFiles/mpf.dir/build.make
libmpf.a: CMakeFiles/mpf.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Linking CXX static library libmpf.a"
	$(CMAKE_COMMAND) -P CMakeFiles/mpf.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/mpf.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/mpf.dir/build: libmpf.a
.PHONY : CMakeFiles/mpf.dir/build

CMakeFiles/mpf.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/mpf.dir/cmake_clean.cmake
.PHONY : CMakeFiles/mpf.dir/clean

CMakeFiles/mpf.dir/depend:
	cd /home/<USER>/workspace/maxi/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/workspace/maxi /home/<USER>/workspace/maxi /home/<USER>/workspace/maxi/build /home/<USER>/workspace/maxi/build /home/<USER>/workspace/maxi/build/CMakeFiles/mpf.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/mpf.dir/depend

