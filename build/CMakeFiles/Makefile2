# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/workspace/maxi

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/workspace/maxi/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/utils.dir/all
all: CMakeFiles/yolo_infer.dir/all
all: CMakeFiles/mpf.dir/all
all: CMakeFiles/multi_process_framework.dir/all
all: CMakeFiles/worker_process.dir/all
all: CMakeFiles/daemon_process.dir/all
all: CMakeFiles/yolov8-multi-gpu.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/utils.dir/clean
clean: CMakeFiles/yolo_infer.dir/clean
clean: CMakeFiles/mpf.dir/clean
clean: CMakeFiles/multi_process_framework.dir/clean
clean: CMakeFiles/worker_process.dir/clean
clean: CMakeFiles/daemon_process.dir/clean
clean: CMakeFiles/yolov8-multi-gpu.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/utils.dir

# All Build rule for target.
CMakeFiles/utils.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=14,15,16 "Built target utils"
.PHONY : CMakeFiles/utils.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/utils.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/utils.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 0
.PHONY : CMakeFiles/utils.dir/rule

# Convenience name for target.
utils: CMakeFiles/utils.dir/rule
.PHONY : utils

# clean rule for target.
CMakeFiles/utils.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/clean
.PHONY : CMakeFiles/utils.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/yolo_infer.dir

# All Build rule for target.
CMakeFiles/yolo_infer.dir/all: CMakeFiles/utils.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=19,20,21,22,23,24,25,26,27,28,29,30,31,32 "Built target yolo_infer"
.PHONY : CMakeFiles/yolo_infer.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/yolo_infer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/yolo_infer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 0
.PHONY : CMakeFiles/yolo_infer.dir/rule

# Convenience name for target.
yolo_infer: CMakeFiles/yolo_infer.dir/rule
.PHONY : yolo_infer

# clean rule for target.
CMakeFiles/yolo_infer.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/clean
.PHONY : CMakeFiles/yolo_infer.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/mpf.dir

# All Build rule for target.
CMakeFiles/mpf.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/mpf.dir/all: CMakeFiles/yolo_infer.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=3,4,5,6,7,8,9,10,11 "Built target mpf"
.PHONY : CMakeFiles/mpf.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/mpf.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 26
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/mpf.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 0
.PHONY : CMakeFiles/mpf.dir/rule

# Convenience name for target.
mpf: CMakeFiles/mpf.dir/rule
.PHONY : mpf

# clean rule for target.
CMakeFiles/mpf.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/clean
.PHONY : CMakeFiles/mpf.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/multi_process_framework.dir

# All Build rule for target.
CMakeFiles/multi_process_framework.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/multi_process_framework.dir/all: CMakeFiles/yolo_infer.dir/all
CMakeFiles/multi_process_framework.dir/all: CMakeFiles/mpf.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/multi_process_framework.dir/build.make CMakeFiles/multi_process_framework.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/multi_process_framework.dir/build.make CMakeFiles/multi_process_framework.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=12,13 "Built target multi_process_framework"
.PHONY : CMakeFiles/multi_process_framework.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/multi_process_framework.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 28
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/multi_process_framework.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 0
.PHONY : CMakeFiles/multi_process_framework.dir/rule

# Convenience name for target.
multi_process_framework: CMakeFiles/multi_process_framework.dir/rule
.PHONY : multi_process_framework

# clean rule for target.
CMakeFiles/multi_process_framework.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/multi_process_framework.dir/build.make CMakeFiles/multi_process_framework.dir/clean
.PHONY : CMakeFiles/multi_process_framework.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/worker_process.dir

# All Build rule for target.
CMakeFiles/worker_process.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/worker_process.dir/all: CMakeFiles/yolo_infer.dir/all
CMakeFiles/worker_process.dir/all: CMakeFiles/mpf.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/worker_process.dir/build.make CMakeFiles/worker_process.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/worker_process.dir/build.make CMakeFiles/worker_process.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=17,18 "Built target worker_process"
.PHONY : CMakeFiles/worker_process.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/worker_process.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 28
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/worker_process.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 0
.PHONY : CMakeFiles/worker_process.dir/rule

# Convenience name for target.
worker_process: CMakeFiles/worker_process.dir/rule
.PHONY : worker_process

# clean rule for target.
CMakeFiles/worker_process.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/worker_process.dir/build.make CMakeFiles/worker_process.dir/clean
.PHONY : CMakeFiles/worker_process.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/daemon_process.dir

# All Build rule for target.
CMakeFiles/daemon_process.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/daemon_process.dir/all: CMakeFiles/yolo_infer.dir/all
CMakeFiles/daemon_process.dir/all: CMakeFiles/mpf.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_process.dir/build.make CMakeFiles/daemon_process.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_process.dir/build.make CMakeFiles/daemon_process.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=1,2 "Built target daemon_process"
.PHONY : CMakeFiles/daemon_process.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/daemon_process.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 28
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/daemon_process.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 0
.PHONY : CMakeFiles/daemon_process.dir/rule

# Convenience name for target.
daemon_process: CMakeFiles/daemon_process.dir/rule
.PHONY : daemon_process

# clean rule for target.
CMakeFiles/daemon_process.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_process.dir/build.make CMakeFiles/daemon_process.dir/clean
.PHONY : CMakeFiles/daemon_process.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/yolov8-multi-gpu.dir

# All Build rule for target.
CMakeFiles/yolov8-multi-gpu.dir/all: CMakeFiles/utils.dir/all
CMakeFiles/yolov8-multi-gpu.dir/all: CMakeFiles/yolo_infer.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolov8-multi-gpu.dir/build.make CMakeFiles/yolov8-multi-gpu.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolov8-multi-gpu.dir/build.make CMakeFiles/yolov8-multi-gpu.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=33,34 "Built target yolov8-multi-gpu"
.PHONY : CMakeFiles/yolov8-multi-gpu.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/yolov8-multi-gpu.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 19
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/yolov8-multi-gpu.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 0
.PHONY : CMakeFiles/yolov8-multi-gpu.dir/rule

# Convenience name for target.
yolov8-multi-gpu: CMakeFiles/yolov8-multi-gpu.dir/rule
.PHONY : yolov8-multi-gpu

# clean rule for target.
CMakeFiles/yolov8-multi-gpu.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolov8-multi-gpu.dir/build.make CMakeFiles/yolov8-multi-gpu.dir/clean
.PHONY : CMakeFiles/yolov8-multi-gpu.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

