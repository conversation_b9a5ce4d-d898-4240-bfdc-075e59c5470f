# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/workspace/maxi

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/workspace/maxi/build

# Include any dependencies generated for this target.
include CMakeFiles/multi_process_framework.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/multi_process_framework.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/multi_process_framework.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/multi_process_framework.dir/flags.make

CMakeFiles/multi_process_framework.dir/app/main.cpp.o: CMakeFiles/multi_process_framework.dir/flags.make
CMakeFiles/multi_process_framework.dir/app/main.cpp.o: /home/<USER>/workspace/maxi/app/main.cpp
CMakeFiles/multi_process_framework.dir/app/main.cpp.o: CMakeFiles/multi_process_framework.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/multi_process_framework.dir/app/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/multi_process_framework.dir/app/main.cpp.o -MF CMakeFiles/multi_process_framework.dir/app/main.cpp.o.d -o CMakeFiles/multi_process_framework.dir/app/main.cpp.o -c /home/<USER>/workspace/maxi/app/main.cpp

CMakeFiles/multi_process_framework.dir/app/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/multi_process_framework.dir/app/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/app/main.cpp > CMakeFiles/multi_process_framework.dir/app/main.cpp.i

CMakeFiles/multi_process_framework.dir/app/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/multi_process_framework.dir/app/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/app/main.cpp -o CMakeFiles/multi_process_framework.dir/app/main.cpp.s

# Object files for target multi_process_framework
multi_process_framework_OBJECTS = \
"CMakeFiles/multi_process_framework.dir/app/main.cpp.o"

# External object files for target multi_process_framework
multi_process_framework_EXTERNAL_OBJECTS =

multi_process_framework: CMakeFiles/multi_process_framework.dir/app/main.cpp.o
multi_process_framework: CMakeFiles/multi_process_framework.dir/build.make
multi_process_framework: libmpf.a
multi_process_framework: libyolo_infer.so
multi_process_framework: libutils.a
multi_process_framework: /usr/lib/x86_64-linux-gnu/libspdlog.so.1.5.0
multi_process_framework: /usr/local/cuda-11.8/lib64/libnvinfer.so.8
multi_process_framework: /usr/local/cuda-11.8/lib64/libnvinfer_plugin.so.8
multi_process_framework: /usr/local/cuda-11.8/lib64/libnvonnxparser.so.8
multi_process_framework: /usr/local/cuda-11.8/lib64/libcudart_static.a
multi_process_framework: /usr/lib/x86_64-linux-gnu/librt.so
multi_process_framework: /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_face.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_text.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_video.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.2.0
multi_process_framework: /usr/lib/x86_64-linux-gnu/libopencv_core.so.4.2.0
multi_process_framework: CMakeFiles/multi_process_framework.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/workspace/maxi/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable multi_process_framework"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/multi_process_framework.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/multi_process_framework.dir/build: multi_process_framework
.PHONY : CMakeFiles/multi_process_framework.dir/build

CMakeFiles/multi_process_framework.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/multi_process_framework.dir/cmake_clean.cmake
.PHONY : CMakeFiles/multi_process_framework.dir/clean

CMakeFiles/multi_process_framework.dir/depend:
	cd /home/<USER>/workspace/maxi/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/workspace/maxi /home/<USER>/workspace/maxi /home/<USER>/workspace/maxi/build /home/<USER>/workspace/maxi/build /home/<USER>/workspace/maxi/build/CMakeFiles/multi_process_framework.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/multi_process_framework.dir/depend

