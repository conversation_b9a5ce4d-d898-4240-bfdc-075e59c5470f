# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/workspace/maxi

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/workspace/maxi/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles /home/<USER>/workspace/maxi/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named utils

# Build rule for target.
utils: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 utils
.PHONY : utils

# fast build rule for target.
utils/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/build
.PHONY : utils/fast

#=============================================================================
# Target rules for targets named yolo_infer

# Build rule for target.
yolo_infer: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 yolo_infer
.PHONY : yolo_infer

# fast build rule for target.
yolo_infer/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/build
.PHONY : yolo_infer/fast

#=============================================================================
# Target rules for targets named mpf

# Build rule for target.
mpf: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mpf
.PHONY : mpf

# fast build rule for target.
mpf/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/build
.PHONY : mpf/fast

#=============================================================================
# Target rules for targets named multi_process_framework

# Build rule for target.
multi_process_framework: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 multi_process_framework
.PHONY : multi_process_framework

# fast build rule for target.
multi_process_framework/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/multi_process_framework.dir/build.make CMakeFiles/multi_process_framework.dir/build
.PHONY : multi_process_framework/fast

#=============================================================================
# Target rules for targets named worker_process

# Build rule for target.
worker_process: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 worker_process
.PHONY : worker_process

# fast build rule for target.
worker_process/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/worker_process.dir/build.make CMakeFiles/worker_process.dir/build
.PHONY : worker_process/fast

#=============================================================================
# Target rules for targets named daemon_process

# Build rule for target.
daemon_process: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 daemon_process
.PHONY : daemon_process

# fast build rule for target.
daemon_process/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_process.dir/build.make CMakeFiles/daemon_process.dir/build
.PHONY : daemon_process/fast

#=============================================================================
# Target rules for targets named yolov8-multi-gpu

# Build rule for target.
yolov8-multi-gpu: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 yolov8-multi-gpu
.PHONY : yolov8-multi-gpu

# fast build rule for target.
yolov8-multi-gpu/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolov8-multi-gpu.dir/build.make CMakeFiles/yolov8-multi-gpu.dir/build
.PHONY : yolov8-multi-gpu/fast

app/daemon.o: app/daemon.cpp.o
.PHONY : app/daemon.o

# target to build an object file
app/daemon.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_process.dir/build.make CMakeFiles/daemon_process.dir/app/daemon.cpp.o
.PHONY : app/daemon.cpp.o

app/daemon.i: app/daemon.cpp.i
.PHONY : app/daemon.i

# target to preprocess a source file
app/daemon.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_process.dir/build.make CMakeFiles/daemon_process.dir/app/daemon.cpp.i
.PHONY : app/daemon.cpp.i

app/daemon.s: app/daemon.cpp.s
.PHONY : app/daemon.s

# target to generate assembly for a file
app/daemon.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_process.dir/build.make CMakeFiles/daemon_process.dir/app/daemon.cpp.s
.PHONY : app/daemon.cpp.s

app/main.o: app/main.cpp.o
.PHONY : app/main.o

# target to build an object file
app/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/multi_process_framework.dir/build.make CMakeFiles/multi_process_framework.dir/app/main.cpp.o
.PHONY : app/main.cpp.o

app/main.i: app/main.cpp.i
.PHONY : app/main.i

# target to preprocess a source file
app/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/multi_process_framework.dir/build.make CMakeFiles/multi_process_framework.dir/app/main.cpp.i
.PHONY : app/main.cpp.i

app/main.s: app/main.cpp.s
.PHONY : app/main.s

# target to generate assembly for a file
app/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/multi_process_framework.dir/build.make CMakeFiles/multi_process_framework.dir/app/main.cpp.s
.PHONY : app/main.cpp.s

app/multi_gpu_main.o: app/multi_gpu_main.cpp.o
.PHONY : app/multi_gpu_main.o

# target to build an object file
app/multi_gpu_main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolov8-multi-gpu.dir/build.make CMakeFiles/yolov8-multi-gpu.dir/app/multi_gpu_main.cpp.o
.PHONY : app/multi_gpu_main.cpp.o

app/multi_gpu_main.i: app/multi_gpu_main.cpp.i
.PHONY : app/multi_gpu_main.i

# target to preprocess a source file
app/multi_gpu_main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolov8-multi-gpu.dir/build.make CMakeFiles/yolov8-multi-gpu.dir/app/multi_gpu_main.cpp.i
.PHONY : app/multi_gpu_main.cpp.i

app/multi_gpu_main.s: app/multi_gpu_main.cpp.s
.PHONY : app/multi_gpu_main.s

# target to generate assembly for a file
app/multi_gpu_main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolov8-multi-gpu.dir/build.make CMakeFiles/yolov8-multi-gpu.dir/app/multi_gpu_main.cpp.s
.PHONY : app/multi_gpu_main.cpp.s

app/worker_main.o: app/worker_main.cpp.o
.PHONY : app/worker_main.o

# target to build an object file
app/worker_main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/worker_process.dir/build.make CMakeFiles/worker_process.dir/app/worker_main.cpp.o
.PHONY : app/worker_main.cpp.o

app/worker_main.i: app/worker_main.cpp.i
.PHONY : app/worker_main.i

# target to preprocess a source file
app/worker_main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/worker_process.dir/build.make CMakeFiles/worker_process.dir/app/worker_main.cpp.i
.PHONY : app/worker_main.cpp.i

app/worker_main.s: app/worker_main.cpp.s
.PHONY : app/worker_main.s

# target to generate assembly for a file
app/worker_main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/worker_process.dir/build.make CMakeFiles/worker_process.dir/app/worker_main.cpp.s
.PHONY : app/worker_main.cpp.s

lib/detect/common.o: lib/detect/common.cpp.o
.PHONY : lib/detect/common.o

# target to build an object file
lib/detect/common.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.o
.PHONY : lib/detect/common.cpp.o

lib/detect/common.i: lib/detect/common.cpp.i
.PHONY : lib/detect/common.i

# target to preprocess a source file
lib/detect/common.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.i
.PHONY : lib/detect/common.cpp.i

lib/detect/common.s: lib/detect/common.cpp.s
.PHONY : lib/detect/common.s

# target to generate assembly for a file
lib/detect/common.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/common.cpp.s
.PHONY : lib/detect/common.cpp.s

lib/detect/infer.o: lib/detect/infer.cu.o
.PHONY : lib/detect/infer.o

# target to build an object file
lib/detect/infer.cu.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.o
.PHONY : lib/detect/infer.cu.o

lib/detect/infer.i: lib/detect/infer.cu.i
.PHONY : lib/detect/infer.i

# target to preprocess a source file
lib/detect/infer.cu.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.i
.PHONY : lib/detect/infer.cu.i

lib/detect/infer.s: lib/detect/infer.cu.s
.PHONY : lib/detect/infer.s

# target to generate assembly for a file
lib/detect/infer.cu.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/infer.cu.s
.PHONY : lib/detect/infer.cu.s

lib/detect/log.o: lib/detect/log.cpp.o
.PHONY : lib/detect/log.o

# target to build an object file
lib/detect/log.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.o
.PHONY : lib/detect/log.cpp.o

lib/detect/log.i: lib/detect/log.cpp.i
.PHONY : lib/detect/log.i

# target to preprocess a source file
lib/detect/log.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.i
.PHONY : lib/detect/log.cpp.i

lib/detect/log.s: lib/detect/log.cpp.s
.PHONY : lib/detect/log.s

# target to generate assembly for a file
lib/detect/log.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/log.cpp.s
.PHONY : lib/detect/log.cpp.s

lib/detect/memory.o: lib/detect/memory.cpp.o
.PHONY : lib/detect/memory.o

# target to build an object file
lib/detect/memory.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.o
.PHONY : lib/detect/memory.cpp.o

lib/detect/memory.i: lib/detect/memory.cpp.i
.PHONY : lib/detect/memory.i

# target to preprocess a source file
lib/detect/memory.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.i
.PHONY : lib/detect/memory.cpp.i

lib/detect/memory.s: lib/detect/memory.cpp.s
.PHONY : lib/detect/memory.s

# target to generate assembly for a file
lib/detect/memory.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/memory.cpp.s
.PHONY : lib/detect/memory.cpp.s

lib/detect/parse_config.o: lib/detect/parse_config.cpp.o
.PHONY : lib/detect/parse_config.o

# target to build an object file
lib/detect/parse_config.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.o
.PHONY : lib/detect/parse_config.cpp.o

lib/detect/parse_config.i: lib/detect/parse_config.cpp.i
.PHONY : lib/detect/parse_config.i

# target to preprocess a source file
lib/detect/parse_config.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.i
.PHONY : lib/detect/parse_config.cpp.i

lib/detect/parse_config.s: lib/detect/parse_config.cpp.s
.PHONY : lib/detect/parse_config.s

# target to generate assembly for a file
lib/detect/parse_config.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/parse_config.cpp.s
.PHONY : lib/detect/parse_config.cpp.s

lib/detect/postprocess.o: lib/detect/postprocess.cu.o
.PHONY : lib/detect/postprocess.o

# target to build an object file
lib/detect/postprocess.cu.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.o
.PHONY : lib/detect/postprocess.cu.o

lib/detect/postprocess.i: lib/detect/postprocess.cu.i
.PHONY : lib/detect/postprocess.i

# target to preprocess a source file
lib/detect/postprocess.cu.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.i
.PHONY : lib/detect/postprocess.cu.i

lib/detect/postprocess.s: lib/detect/postprocess.cu.s
.PHONY : lib/detect/postprocess.s

# target to generate assembly for a file
lib/detect/postprocess.cu.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/postprocess.cu.s
.PHONY : lib/detect/postprocess.cu.s

lib/detect/preprocess.o: lib/detect/preprocess.cu.o
.PHONY : lib/detect/preprocess.o

# target to build an object file
lib/detect/preprocess.cu.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.o
.PHONY : lib/detect/preprocess.cu.o

lib/detect/preprocess.i: lib/detect/preprocess.cu.i
.PHONY : lib/detect/preprocess.i

# target to preprocess a source file
lib/detect/preprocess.cu.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.i
.PHONY : lib/detect/preprocess.cu.i

lib/detect/preprocess.s: lib/detect/preprocess.cu.s
.PHONY : lib/detect/preprocess.s

# target to generate assembly for a file
lib/detect/preprocess.cu.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/preprocess.cu.s
.PHONY : lib/detect/preprocess.cu.s

lib/detect/spdlog.o: lib/detect/spdlog.cpp.o
.PHONY : lib/detect/spdlog.o

# target to build an object file
lib/detect/spdlog.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.o
.PHONY : lib/detect/spdlog.cpp.o

lib/detect/spdlog.i: lib/detect/spdlog.cpp.i
.PHONY : lib/detect/spdlog.i

# target to preprocess a source file
lib/detect/spdlog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.i
.PHONY : lib/detect/spdlog.cpp.i

lib/detect/spdlog.s: lib/detect/spdlog.cpp.s
.PHONY : lib/detect/spdlog.s

# target to generate assembly for a file
lib/detect/spdlog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/spdlog.cpp.s
.PHONY : lib/detect/spdlog.cpp.s

lib/detect/test.o: lib/detect/test.cpp.o
.PHONY : lib/detect/test.o

# target to build an object file
lib/detect/test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.o
.PHONY : lib/detect/test.cpp.o

lib/detect/test.i: lib/detect/test.cpp.i
.PHONY : lib/detect/test.i

# target to preprocess a source file
lib/detect/test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.i
.PHONY : lib/detect/test.cpp.i

lib/detect/test.s: lib/detect/test.cpp.s
.PHONY : lib/detect/test.s

# target to generate assembly for a file
lib/detect/test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/test.cpp.s
.PHONY : lib/detect/test.cpp.s

lib/detect/timer.o: lib/detect/timer.cpp.o
.PHONY : lib/detect/timer.o

# target to build an object file
lib/detect/timer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.o
.PHONY : lib/detect/timer.cpp.o

lib/detect/timer.i: lib/detect/timer.cpp.i
.PHONY : lib/detect/timer.i

# target to preprocess a source file
lib/detect/timer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.i
.PHONY : lib/detect/timer.cpp.i

lib/detect/timer.s: lib/detect/timer.cpp.s
.PHONY : lib/detect/timer.s

# target to generate assembly for a file
lib/detect/timer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/timer.cpp.s
.PHONY : lib/detect/timer.cpp.s

lib/detect/util.o: lib/detect/util.cpp.o
.PHONY : lib/detect/util.o

# target to build an object file
lib/detect/util.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.o
.PHONY : lib/detect/util.cpp.o

lib/detect/util.i: lib/detect/util.cpp.i
.PHONY : lib/detect/util.i

# target to preprocess a source file
lib/detect/util.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.i
.PHONY : lib/detect/util.cpp.i

lib/detect/util.s: lib/detect/util.cpp.s
.PHONY : lib/detect/util.s

# target to generate assembly for a file
lib/detect/util.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/util.cpp.s
.PHONY : lib/detect/util.cpp.s

lib/detect/yolo.o: lib/detect/yolo.cpp.o
.PHONY : lib/detect/yolo.o

# target to build an object file
lib/detect/yolo.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.o
.PHONY : lib/detect/yolo.cpp.o

lib/detect/yolo.i: lib/detect/yolo.cpp.i
.PHONY : lib/detect/yolo.i

# target to preprocess a source file
lib/detect/yolo.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.i
.PHONY : lib/detect/yolo.cpp.i

lib/detect/yolo.s: lib/detect/yolo.cpp.s
.PHONY : lib/detect/yolo.s

# target to generate assembly for a file
lib/detect/yolo.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/yolo.cpp.s
.PHONY : lib/detect/yolo.cpp.s

lib/detect/yolo_infer.o: lib/detect/yolo_infer.cpp.o
.PHONY : lib/detect/yolo_infer.o

# target to build an object file
lib/detect/yolo_infer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.o
.PHONY : lib/detect/yolo_infer.cpp.o

lib/detect/yolo_infer.i: lib/detect/yolo_infer.cpp.i
.PHONY : lib/detect/yolo_infer.i

# target to preprocess a source file
lib/detect/yolo_infer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.i
.PHONY : lib/detect/yolo_infer.cpp.i

lib/detect/yolo_infer.s: lib/detect/yolo_infer.cpp.s
.PHONY : lib/detect/yolo_infer.s

# target to generate assembly for a file
lib/detect/yolo_infer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/yolo_infer.dir/build.make CMakeFiles/yolo_infer.dir/lib/detect/yolo_infer.cpp.s
.PHONY : lib/detect/yolo_infer.cpp.s

lib/mpf/daemon_process.o: lib/mpf/daemon_process.cpp.o
.PHONY : lib/mpf/daemon_process.o

# target to build an object file
lib/mpf/daemon_process.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.o
.PHONY : lib/mpf/daemon_process.cpp.o

lib/mpf/daemon_process.i: lib/mpf/daemon_process.cpp.i
.PHONY : lib/mpf/daemon_process.i

# target to preprocess a source file
lib/mpf/daemon_process.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.i
.PHONY : lib/mpf/daemon_process.cpp.i

lib/mpf/daemon_process.s: lib/mpf/daemon_process.cpp.s
.PHONY : lib/mpf/daemon_process.s

# target to generate assembly for a file
lib/mpf/daemon_process.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/daemon_process.cpp.s
.PHONY : lib/mpf/daemon_process.cpp.s

lib/mpf/gpu_detector.o: lib/mpf/gpu_detector.cpp.o
.PHONY : lib/mpf/gpu_detector.o

# target to build an object file
lib/mpf/gpu_detector.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.o
.PHONY : lib/mpf/gpu_detector.cpp.o

lib/mpf/gpu_detector.i: lib/mpf/gpu_detector.cpp.i
.PHONY : lib/mpf/gpu_detector.i

# target to preprocess a source file
lib/mpf/gpu_detector.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.i
.PHONY : lib/mpf/gpu_detector.cpp.i

lib/mpf/gpu_detector.s: lib/mpf/gpu_detector.cpp.s
.PHONY : lib/mpf/gpu_detector.s

# target to generate assembly for a file
lib/mpf/gpu_detector.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/gpu_detector.cpp.s
.PHONY : lib/mpf/gpu_detector.cpp.s

lib/mpf/main_process.o: lib/mpf/main_process.cpp.o
.PHONY : lib/mpf/main_process.o

# target to build an object file
lib/mpf/main_process.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.o
.PHONY : lib/mpf/main_process.cpp.o

lib/mpf/main_process.i: lib/mpf/main_process.cpp.i
.PHONY : lib/mpf/main_process.i

# target to preprocess a source file
lib/mpf/main_process.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.i
.PHONY : lib/mpf/main_process.cpp.i

lib/mpf/main_process.s: lib/mpf/main_process.cpp.s
.PHONY : lib/mpf/main_process.s

# target to generate assembly for a file
lib/mpf/main_process.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/main_process.cpp.s
.PHONY : lib/mpf/main_process.cpp.s

lib/mpf/message.o: lib/mpf/message.cpp.o
.PHONY : lib/mpf/message.o

# target to build an object file
lib/mpf/message.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/message.cpp.o
.PHONY : lib/mpf/message.cpp.o

lib/mpf/message.i: lib/mpf/message.cpp.i
.PHONY : lib/mpf/message.i

# target to preprocess a source file
lib/mpf/message.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/message.cpp.i
.PHONY : lib/mpf/message.cpp.i

lib/mpf/message.s: lib/mpf/message.cpp.s
.PHONY : lib/mpf/message.s

# target to generate assembly for a file
lib/mpf/message.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/message.cpp.s
.PHONY : lib/mpf/message.cpp.s

lib/mpf/multi_process_framework.o: lib/mpf/multi_process_framework.cpp.o
.PHONY : lib/mpf/multi_process_framework.o

# target to build an object file
lib/mpf/multi_process_framework.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.o
.PHONY : lib/mpf/multi_process_framework.cpp.o

lib/mpf/multi_process_framework.i: lib/mpf/multi_process_framework.cpp.i
.PHONY : lib/mpf/multi_process_framework.i

# target to preprocess a source file
lib/mpf/multi_process_framework.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.i
.PHONY : lib/mpf/multi_process_framework.cpp.i

lib/mpf/multi_process_framework.s: lib/mpf/multi_process_framework.cpp.s
.PHONY : lib/mpf/multi_process_framework.s

# target to generate assembly for a file
lib/mpf/multi_process_framework.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/multi_process_framework.cpp.s
.PHONY : lib/mpf/multi_process_framework.cpp.s

lib/mpf/task_manager.o: lib/mpf/task_manager.cpp.o
.PHONY : lib/mpf/task_manager.o

# target to build an object file
lib/mpf/task_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.o
.PHONY : lib/mpf/task_manager.cpp.o

lib/mpf/task_manager.i: lib/mpf/task_manager.cpp.i
.PHONY : lib/mpf/task_manager.i

# target to preprocess a source file
lib/mpf/task_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.i
.PHONY : lib/mpf/task_manager.cpp.i

lib/mpf/task_manager.s: lib/mpf/task_manager.cpp.s
.PHONY : lib/mpf/task_manager.s

# target to generate assembly for a file
lib/mpf/task_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/task_manager.cpp.s
.PHONY : lib/mpf/task_manager.cpp.s

lib/mpf/windows_reporter.o: lib/mpf/windows_reporter.cpp.o
.PHONY : lib/mpf/windows_reporter.o

# target to build an object file
lib/mpf/windows_reporter.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.o
.PHONY : lib/mpf/windows_reporter.cpp.o

lib/mpf/windows_reporter.i: lib/mpf/windows_reporter.cpp.i
.PHONY : lib/mpf/windows_reporter.i

# target to preprocess a source file
lib/mpf/windows_reporter.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.i
.PHONY : lib/mpf/windows_reporter.cpp.i

lib/mpf/windows_reporter.s: lib/mpf/windows_reporter.cpp.s
.PHONY : lib/mpf/windows_reporter.s

# target to generate assembly for a file
lib/mpf/windows_reporter.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/windows_reporter.cpp.s
.PHONY : lib/mpf/windows_reporter.cpp.s

lib/mpf/worker_process.o: lib/mpf/worker_process.cpp.o
.PHONY : lib/mpf/worker_process.o

# target to build an object file
lib/mpf/worker_process.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.o
.PHONY : lib/mpf/worker_process.cpp.o

lib/mpf/worker_process.i: lib/mpf/worker_process.cpp.i
.PHONY : lib/mpf/worker_process.i

# target to preprocess a source file
lib/mpf/worker_process.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.i
.PHONY : lib/mpf/worker_process.cpp.i

lib/mpf/worker_process.s: lib/mpf/worker_process.cpp.s
.PHONY : lib/mpf/worker_process.s

# target to generate assembly for a file
lib/mpf/worker_process.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mpf.dir/build.make CMakeFiles/mpf.dir/lib/mpf/worker_process.cpp.s
.PHONY : lib/mpf/worker_process.cpp.s

lib/utils/config_parser.o: lib/utils/config_parser.cpp.o
.PHONY : lib/utils/config_parser.o

# target to build an object file
lib/utils/config_parser.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/lib/utils/config_parser.cpp.o
.PHONY : lib/utils/config_parser.cpp.o

lib/utils/config_parser.i: lib/utils/config_parser.cpp.i
.PHONY : lib/utils/config_parser.i

# target to preprocess a source file
lib/utils/config_parser.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/lib/utils/config_parser.cpp.i
.PHONY : lib/utils/config_parser.cpp.i

lib/utils/config_parser.s: lib/utils/config_parser.cpp.s
.PHONY : lib/utils/config_parser.s

# target to generate assembly for a file
lib/utils/config_parser.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/lib/utils/config_parser.cpp.s
.PHONY : lib/utils/config_parser.cpp.s

lib/utils/logger.o: lib/utils/logger.cpp.o
.PHONY : lib/utils/logger.o

# target to build an object file
lib/utils/logger.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/lib/utils/logger.cpp.o
.PHONY : lib/utils/logger.cpp.o

lib/utils/logger.i: lib/utils/logger.cpp.i
.PHONY : lib/utils/logger.i

# target to preprocess a source file
lib/utils/logger.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/lib/utils/logger.cpp.i
.PHONY : lib/utils/logger.cpp.i

lib/utils/logger.s: lib/utils/logger.cpp.s
.PHONY : lib/utils/logger.s

# target to generate assembly for a file
lib/utils/logger.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/utils.dir/build.make CMakeFiles/utils.dir/lib/utils/logger.cpp.s
.PHONY : lib/utils/logger.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... daemon_process"
	@echo "... mpf"
	@echo "... multi_process_framework"
	@echo "... utils"
	@echo "... worker_process"
	@echo "... yolo_infer"
	@echo "... yolov8-multi-gpu"
	@echo "... app/daemon.o"
	@echo "... app/daemon.i"
	@echo "... app/daemon.s"
	@echo "... app/main.o"
	@echo "... app/main.i"
	@echo "... app/main.s"
	@echo "... app/multi_gpu_main.o"
	@echo "... app/multi_gpu_main.i"
	@echo "... app/multi_gpu_main.s"
	@echo "... app/worker_main.o"
	@echo "... app/worker_main.i"
	@echo "... app/worker_main.s"
	@echo "... lib/detect/common.o"
	@echo "... lib/detect/common.i"
	@echo "... lib/detect/common.s"
	@echo "... lib/detect/infer.o"
	@echo "... lib/detect/infer.i"
	@echo "... lib/detect/infer.s"
	@echo "... lib/detect/log.o"
	@echo "... lib/detect/log.i"
	@echo "... lib/detect/log.s"
	@echo "... lib/detect/memory.o"
	@echo "... lib/detect/memory.i"
	@echo "... lib/detect/memory.s"
	@echo "... lib/detect/parse_config.o"
	@echo "... lib/detect/parse_config.i"
	@echo "... lib/detect/parse_config.s"
	@echo "... lib/detect/postprocess.o"
	@echo "... lib/detect/postprocess.i"
	@echo "... lib/detect/postprocess.s"
	@echo "... lib/detect/preprocess.o"
	@echo "... lib/detect/preprocess.i"
	@echo "... lib/detect/preprocess.s"
	@echo "... lib/detect/spdlog.o"
	@echo "... lib/detect/spdlog.i"
	@echo "... lib/detect/spdlog.s"
	@echo "... lib/detect/test.o"
	@echo "... lib/detect/test.i"
	@echo "... lib/detect/test.s"
	@echo "... lib/detect/timer.o"
	@echo "... lib/detect/timer.i"
	@echo "... lib/detect/timer.s"
	@echo "... lib/detect/util.o"
	@echo "... lib/detect/util.i"
	@echo "... lib/detect/util.s"
	@echo "... lib/detect/yolo.o"
	@echo "... lib/detect/yolo.i"
	@echo "... lib/detect/yolo.s"
	@echo "... lib/detect/yolo_infer.o"
	@echo "... lib/detect/yolo_infer.i"
	@echo "... lib/detect/yolo_infer.s"
	@echo "... lib/mpf/daemon_process.o"
	@echo "... lib/mpf/daemon_process.i"
	@echo "... lib/mpf/daemon_process.s"
	@echo "... lib/mpf/gpu_detector.o"
	@echo "... lib/mpf/gpu_detector.i"
	@echo "... lib/mpf/gpu_detector.s"
	@echo "... lib/mpf/main_process.o"
	@echo "... lib/mpf/main_process.i"
	@echo "... lib/mpf/main_process.s"
	@echo "... lib/mpf/message.o"
	@echo "... lib/mpf/message.i"
	@echo "... lib/mpf/message.s"
	@echo "... lib/mpf/multi_process_framework.o"
	@echo "... lib/mpf/multi_process_framework.i"
	@echo "... lib/mpf/multi_process_framework.s"
	@echo "... lib/mpf/task_manager.o"
	@echo "... lib/mpf/task_manager.i"
	@echo "... lib/mpf/task_manager.s"
	@echo "... lib/mpf/windows_reporter.o"
	@echo "... lib/mpf/windows_reporter.i"
	@echo "... lib/mpf/windows_reporter.s"
	@echo "... lib/mpf/worker_process.o"
	@echo "... lib/mpf/worker_process.i"
	@echo "... lib/mpf/worker_process.s"
	@echo "... lib/utils/config_parser.o"
	@echo "... lib/utils/config_parser.i"
	@echo "... lib/utils/config_parser.s"
	@echo "... lib/utils/logger.o"
	@echo "... lib/utils/logger.i"
	@echo "... lib/utils/logger.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

