[2025-06-17 09:23:52.684] [info] [538287] 日志系统初始化完成，日志文件: daemon_process.log
[2025-06-17 09:23:52.684] [info] [538287] 成功加载配置文件: ../config.json
[2025-06-17 09:23:52.684] [info] [538287] PID文件已创建: /tmp/daemon_process.pid
[2025-06-17 09:23:52.684] [info] [538287] 守护进程已启动，监听端口: 9000
[2025-06-17 09:23:52.684] [info] [538288] 监听线程已启动，等待客户端连接...
[2025-06-17 09:23:55.277] [info] [538288] 接收到来自 127.0.0.1 的连接
[2025-06-17 09:23:55.277] [info] [538288] 接收到请求: {"input_dir":"/home/<USER>/workspace/maxi/test_large_imgs","output_dir":"/home/<USER>/workspace/maxi/test_results","type":"process_images"}
[2025-06-17 09:23:55.277] [info] [538288] 成功加载配置文件: ../config.json
[2025-06-17 09:23:55.277] [info] [538288] 设置 YOLOv8 配置...
[2025-06-17 09:23:55.277] [info] [538288] 引擎文件: /home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.transd.engine
[2025-06-17 09:23:55.277] [info] [538288] 配置文件: /home/<USER>/workspace/maxi/model_config.json
[2025-06-17 09:23:55.277] [info] [538288] 任务名称: yolov8_detection
[2025-06-17 09:23:55.277] [info] [538288] 日志文件: yolov8_detection.log
[2025-06-17 09:23:55.277] [info] [538288] YOLOv8 配置已设置
[2025-06-17 09:23:55.277] [info] [538288] 引擎文件: /home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.transd.engine
[2025-06-17 09:23:55.277] [info] [538288] 配置文件: /home/<USER>/workspace/maxi/model_config.json
[2025-06-17 09:23:55.277] [info] [538288] 任务名称: yolov8_detection
[2025-06-17 09:23:55.277] [info] [538288] 日志文件: yolov8_detection.log
