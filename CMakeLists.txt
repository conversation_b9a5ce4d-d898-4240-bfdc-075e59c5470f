cmake_minimum_required(VERSION 3.10)
project(maxi_framework LANGUAGES CXX CUDA)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# 设置 CUDA 架构
set(CMAKE_CUDA_ARCHITECTURES 86)

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug)
endif()

# 查找必要的包
find_package(Boost REQUIRED COMPONENTS system filesystem program_options)
find_package(OpenCV REQUIRED)
find_package(nlohmann_json REQUIRED)
find_package(CUDA REQUIRED)
find_package(spdlog REQUIRED)
find_package(PkgConfig REQUIRED)
pkg_check_modules(ZeroMQ REQUIRED libzmq)

# 设置 TensorRT 路径
set(SYS_TRT "/usr/include/x86_64-linux-gnu/")
set(TRT_LIB "/usr/local/cuda-11.8/lib64/libnvinfer.so.8")
set(TRT_PLUGIN "/usr/local/cuda-11.8/lib64/libnvinfer_plugin.so.8")
set(TRT_ONNXPS "/usr/local/cuda-11.8/lib64/libnvonnxparser.so.8")

# 设置头文件路径
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${SYS_TRT}
    ${CUDA_INCLUDE_DIRS}
    ${OpenCV_INCLUDE_DIRS}
    ${Boost_INCLUDE_DIRS}
    ${ZeroMQ_INCLUDE_DIRS}
)

# 设置链接库路径
link_directories(
    ${CUDA_LIBRARY_DIRS}
    ${OpenCV_LIBRARY_DIRS}
    ${Boost_LIBRARY_DIRS}
    ${ZeroMQ_LIBRARY_DIRS}
)

#################################################
# 1. utils 库 - 通用工具库
#################################################

# 设置 utils 库源文件
set(UTILS_LIB_SOURCES
    lib/utils/logger.cpp
    lib/utils/config_parser.cpp
)

# 创建 utils 静态库
add_library(utils STATIC ${UTILS_LIB_SOURCES})

# 链接 utils 库
target_link_libraries(utils PUBLIC
    nlohmann_json::nlohmann_json
    spdlog::spdlog
)

#################################################
# 2. yolo_infer 库 - YOLOv8 检测库
#################################################

# 设置 yolo_infer 库源文件
file(GLOB_RECURSE DETECT_LIB_SOURCES
    lib/detect/*.cpp
    lib/detect/*.cu
)

# 创建 yolo_infer 共享库
add_library(yolo_infer SHARED ${DETECT_LIB_SOURCES})

# 链接 yolo_infer 库
target_link_libraries(yolo_infer PUBLIC
    utils
    ${OpenCV_LIBRARIES}
    ${TRT_LIB}
    ${TRT_PLUGIN}
    ${TRT_ONNXPS}
    ${CUDA_LIBRARIES}
    nlohmann_json::nlohmann_json
    spdlog::spdlog
)

#################################################
# 3. mpf 库 - 多进程框架库
#################################################

# 设置 mpf 库源文件
set(MPF_LIB_SOURCES
    lib/mpf/message.cpp
    lib/mpf/task_manager.cpp
    lib/mpf/gpu_detector.cpp
    lib/mpf/worker_process.cpp
    lib/mpf/main_process.cpp
    lib/mpf/multi_process_framework.cpp
    lib/mpf/windows_reporter.cpp
    lib/mpf/daemon_process.cpp
)

# 创建 mpf 静态库
add_library(mpf STATIC ${MPF_LIB_SOURCES})

# 链接 mpf 库
target_link_libraries(mpf PUBLIC
    utils
    yolo_infer
    ${Boost_LIBRARIES}
    ${OpenCV_LIBS}
    ${ZeroMQ_LIBRARIES}
    stdc++fs
)

#################################################
# 4. 可执行文件
#################################################

# 多进程框架主进程
add_executable(multi_process_framework app/main.cpp)
target_link_libraries(multi_process_framework PRIVATE mpf)

# 工作进程
add_executable(worker_process app/worker_main.cpp)
target_link_libraries(worker_process PRIVATE mpf)

# 守护进程
add_executable(daemon_process app/daemon.cpp)
target_link_libraries(daemon_process PRIVATE mpf)

# YOLOv8 多 GPU 测试程序
add_executable(yolov8-multi-gpu app/multi_gpu_main.cpp)
target_link_libraries(yolov8-multi-gpu PRIVATE
    yolo_infer
    ${OpenCV_LIBRARIES}
    ${CUDA_LIBRARIES}
    nlohmann_json::nlohmann_json
    spdlog::spdlog
    utils
)

#################################################
# 5. 安装目标
#################################################

# 安装可执行文件
install(TARGETS
    multi_process_framework
    worker_process
    daemon_process
    yolov8-multi-gpu
    RUNTIME DESTINATION bin
)

# 安装库文件
install(TARGETS
    mpf
    utils
    yolo_infer
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
)

# 安装头文件
install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp"
)
