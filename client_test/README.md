# 客户端测试工具

这个目录包含用于测试多进程图像处理框架守护进程的客户端工具。主要包括一个简单的测试脚本和一个进度接收客户端程序。

## 客户端测试脚本

`client_test.sh` 是一个简单的测试脚本，用于向守护进程发送处理请求。

### 使用方法

```bash
# 在项目根目录下运行
./client_test.sh
```

这个脚本会向守护进程发送处理请求，处理指定目录下的图片。默认情况下，它会连接到本地的守护进程（127.0.0.1:9000），并请求处理 `test_large_imgs` 目录中的图片，将结果保存到 `test_results` 目录。

### 脚本内容

```bash
#!/bin/bash

# 发送处理请求到守护进程
echo "已发送处理请求"
curl -X POST -H "Content-Type: application/json" -d '{"input_dir":"/home/<USER>/workspace/maxi/test_large_imgs","output_dir":"/home/<USER>/workspace/maxi/test_results","type":"process_images"}' http://127.0.0.1:9000/process

# 等待处理完成
while true; do
    # 查询处理状态
    status=$(curl -s http://127.0.0.1:9000/status)

    # 检查是否处理完成
    if [[ $status == *"空闲"* ]]; then
        echo "处理完成"
        break
    fi

    # 显示当前进度
    echo "当前状态: $status"

    # 等待1秒
    sleep 1
done
```

## 进度接收客户端

`progress_receiver` 是一个TCP服务器程序，用于接收并显示图像处理框架发送的进度信息。它可以在Ubuntu、Windows或任何支持TCP/IP的系统上运行。

### 编译

#### 在Ubuntu上编译

```bash
mkdir -p build && cd build
cmake ..
make
```

#### 在Windows上编译（使用MinGW或MSVC）

使用MinGW:
```bash
mkdir build
cd build
cmake -G "MinGW Makefiles" ..
mingw32-make
```

使用MSVC:
```bash
mkdir build
cd build
cmake -G "Visual Studio 16 2019" ..
cmake --build . --config Release
```

### 使用方法

```bash
./progress_receiver [--port <端口>] [--log <日志文件>] [--help]
```

参数说明：
- `--port <端口>`: 指定监听端口，默认为8080
- `--log <日志文件>`: 指定日志文件路径，启用日志记录
- `--help`: 显示帮助信息

为了兼容旧版本，也支持直接指定端口：
```bash
./progress_receiver <端口>
```

### 示例

```bash
# 使用默认端口8080
./progress_receiver

# 指定端口9000
./progress_receiver --port 9000

# 启用日志记录
./progress_receiver --port 8080 --log progress.log
```

启动后，程序将监听指定端口，等待图像处理框架发送的进度信息。收到信息后，会在控制台显示处理进度。

### 接收的消息格式

接收的消息为JSON格式，包含以下字段：

```json
{
  "processed_images": 123,
  "total_images": 1000,
  "timestamp": 1629123456789
}
```

### 功能特点

- **实时进度显示**: 接收并显示图像处理的实时进度
- **状态监控**: 每5秒自动显示处理速度和预计剩余时间
- **日志记录**: 可选择将所有进度信息记录到日志文件
- **错误处理**: 健壮的错误处理机制，能够应对网络问题
- **跨平台兼容**: 可在Ubuntu、Windows等多种操作系统上运行

## 与守护进程的交互

客户端可以通过HTTP请求与守护进程交互，支持以下操作：

### 1. 发送处理请求

```bash
curl -X POST -H "Content-Type: application/json" -d '{"input_dir":"<输入目录>","output_dir":"<输出目录>","type":"process_images"}' http://127.0.0.1:9000/process
```

### 2. 查询处理状态

```bash
curl http://127.0.0.1:9000/status
```

### 3. 停止处理

```bash
curl http://127.0.0.1:9000/stop
```

## 多GPU支持

客户端可以在请求中指定要使用的GPU ID：

```bash
curl -X POST -H "Content-Type: application/json" -d '{"input_dir":"<输入目录>","output_dir":"<输出目录>","type":"process_images","gpu_id":"0,1"}' http://127.0.0.1:9000/process
```

这将使守护进程在GPU 0和GPU 1上创建工作进程。如果不指定GPU ID，守护进程将使用配置文件中的设置或自动检测可用的GPU。

## 注意事项

- 确保防火墙允许指定端口的TCP连接
- 在Windows上运行时，可能需要以管理员权限运行，特别是使用低于1024的端口时
- 按Ctrl+C可以退出程序
- 如果启用日志记录，确保有足够的磁盘空间
- 程序会自动处理连接中断和重连，无需手动干预
- 确保守护进程已经启动并正在监听指定的端口
