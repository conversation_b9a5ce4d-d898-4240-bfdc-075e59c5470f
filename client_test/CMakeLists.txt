cmake_minimum_required(VERSION 3.10)
project(progress_receiver)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找必要的包
find_package(nlohmann_json QUIET)
find_package(Boost REQUIRED COMPONENTS program_options)

# 如果没有找到nlohmann_json，使用FetchContent下载
if(NOT nlohmann_json_FOUND)
    include(FetchContent)
    FetchContent_Declare(
        nlohmann_json
        GIT_REPOSITORY https://github.com/nlohmann/json.git
        GIT_TAG v3.11.2
    )
    FetchContent_MakeAvailable(nlohmann_json)
endif()

# 添加可执行文件
add_executable(progress_receiver
    progress_receiver.cpp
)

add_executable(process_client
    process_client.cpp
)

# 链接库
target_link_libraries(progress_receiver
    PRIVATE
    nlohmann_json::nlohmann_json
    pthread
)

target_link_libraries(process_client
    PRIVATE
    nlohmann_json::nlohmann_json
    ${Boost_LIBRARIES}
    pthread
)

# 安装目标
install(TARGETS progress_receiver process_client
    RUNTIME DESTINATION bin
)
