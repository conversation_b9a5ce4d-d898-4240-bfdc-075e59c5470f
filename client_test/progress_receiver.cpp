#include <iostream>
#include <string>
#include <cstring>
#include <thread>
#include <atomic>
#include <chrono>
#include <mutex>
#include <vector>
#include <queue>
#include <fstream>
#include <iomanip>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <signal.h>
#include <fcntl.h>
#include <errno.h>
#include <nlohmann/json.hpp>

// 全局变量
std::atomic<bool> running(true);
std::atomic<int> totalProcessed(0);
std::atomic<int> totalImages(0);
std::mutex logMutex;
std::ofstream logFile;
bool enableLogging = false;

// 信号处理函数
void signalHandler(int signum) {
    std::cout << "接收到信号 " << signum << "，准备退出..." << std::endl;
    running = false;
}

// 获取当前时间字符串
std::string getCurrentTimeString() {
    auto now = std::chrono::system_clock::now();
    auto now_time_t = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&now_time_t), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

// 写入日志
void writeLog(const std::string& message) {
    if (!enableLogging) return;

    std::lock_guard<std::mutex> lock(logMutex);
    logFile << "[" << getCurrentTimeString() << "] " << message << std::endl;
    logFile.flush();
}

// 处理客户端连接的函数
void handleClient(int clientSocket) {
    const int bufferSize = 4096;
    char buffer[bufferSize];

    // 设置接收超时
    struct timeval timeout;
    timeout.tv_sec = 2;  // 2秒超时
    timeout.tv_usec = 0;
    if (setsockopt(clientSocket, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
        std::cerr << "设置接收超时失败" << std::endl;
        writeLog("设置接收超时失败");
        close(clientSocket);
        return;
    }

    // 接收数据
    int bytesRead = recv(clientSocket, buffer, bufferSize - 1, 0);
    if (bytesRead <= 0) {
        if (bytesRead == 0) {
            std::cerr << "客户端关闭连接" << std::endl;
            writeLog("客户端关闭连接");
        } else {
            std::cerr << "接收数据失败: " << strerror(errno) << std::endl;
            writeLog("接收数据失败: " + std::string(strerror(errno)));
        }
        close(clientSocket);
        return;
    }

    // 确保字符串以null结尾
    buffer[bytesRead] = '\0';

    // 解析JSON
    try {
        nlohmann::json data = nlohmann::json::parse(buffer);

        // 更新全局计数
        int processed = data["processed_images"].get<int>();
        int total = data["total_images"].get<int>();
        totalProcessed = processed;
        totalImages = total;

        // 计算百分比
        double percentage = (total > 0) ? (processed * 100.0 / total) : 0.0;

        // 获取当前时间
        std::string timeStr = getCurrentTimeString();

        // 构建进度消息
        std::stringstream progressMsg;
        progressMsg << "进度: " << processed << "/" << total
                   << " (" << std::fixed << std::setprecision(2) << percentage << "%)";

        // 打印进度信息
        std::cout << "[" << timeStr << "] " << progressMsg.str() << std::endl;

        // 写入日志
        writeLog(progressMsg.str());

        // 发送确认响应
        std::string response = "OK";
        send(clientSocket, response.c_str(), response.length(), 0);
    } catch (const std::exception& e) {
        std::cerr << "解析JSON失败: " << e.what() << std::endl;
        std::cerr << "接收到的数据: " << buffer << std::endl;
        writeLog("解析JSON失败: " + std::string(e.what()));
        writeLog("接收到的数据: " + std::string(buffer));
    }

    close(clientSocket);
}

// 状态监控线程函数
void statusMonitorThread() {
    int lastProcessed = 0;
    auto lastUpdateTime = std::chrono::steady_clock::now();

    while (running) {
        // 每5秒打印一次状态摘要
        std::this_thread::sleep_for(std::chrono::seconds(5));

        auto currentTime = std::chrono::steady_clock::now();
        auto elapsedSeconds = std::chrono::duration_cast<std::chrono::seconds>(
            currentTime - lastUpdateTime).count();

        int currentProcessed = totalProcessed.load();
        int currentTotal = totalImages.load();

        // 计算处理速度
        double speed = 0.0;
        if (elapsedSeconds > 0) {
            speed = static_cast<double>(currentProcessed - lastProcessed) / elapsedSeconds;
        }

        // 计算剩余时间
        int remainingImages = currentTotal - currentProcessed;
        int estimatedRemainingSeconds = (speed > 0 && remainingImages > 0) ?
            static_cast<int>(remainingImages / speed) : 0;

        // 打印状态摘要
        std::cout << "状态摘要 - 已处理: " << currentProcessed << "/" << currentTotal;
        std::cout << ", 速度: " << std::fixed << std::setprecision(2) << speed << " 图片/秒";
        std::cout << ", 预计剩余时间: " << estimatedRemainingSeconds << " 秒" << std::endl;

        // 写入日志
        std::stringstream statusMsg;
        statusMsg << "状态摘要 - 已处理: " << currentProcessed << "/" << currentTotal
                 << ", 速度: " << std::fixed << std::setprecision(2) << speed << " 图片/秒"
                 << ", 预计剩余时间: " << estimatedRemainingSeconds << " 秒";
        writeLog(statusMsg.str());

        // 更新上次处理数量和时间
        lastProcessed = currentProcessed;
        lastUpdateTime = currentTime;
    }
}

int main(int argc, char* argv[]) {
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);

    // 默认端口
    int port = 8080;
    std::string logFilePath = "";

    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "--port" && i + 1 < argc) {
            port = std::stoi(argv[++i]);
        } else if (arg == "--log" && i + 1 < argc) {
            logFilePath = argv[++i];
            enableLogging = true;
        } else if (arg == "--help") {
            std::cout << "用法: " << argv[0] << " [--port <端口>] [--log <日志文件>] [--help]" << std::endl;
            return 0;
        } else if (i == 1 && arg.find("--") != 0) {
            // 兼容旧的命令行格式
            port = std::stoi(arg);
        }
    }

    // 初始化日志
    if (enableLogging) {
        logFile.open(logFilePath, std::ios::app);
        if (!logFile.is_open()) {
            std::cerr << "无法打开日志文件: " << logFilePath << std::endl;
            enableLogging = false;
        } else {
            writeLog("进度接收服务器启动");
        }
    }

    // 创建套接字
    int serverSocket = socket(AF_INET, SOCK_STREAM, 0);
    if (serverSocket < 0) {
        std::cerr << "创建套接字失败" << std::endl;
        return 1;
    }

    // 设置套接字选项，允许地址重用
    int opt = 1;
    if (setsockopt(serverSocket, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        std::cerr << "设置套接字选项失败" << std::endl;
        close(serverSocket);
        return 1;
    }

    // 绑定地址
    struct sockaddr_in serverAddr;
    memset(&serverAddr, 0, sizeof(serverAddr));
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(port);

    if (bind(serverSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) < 0) {
        std::cerr << "绑定地址失败" << std::endl;
        close(serverSocket);
        return 1;
    }

    // 监听连接
    if (listen(serverSocket, 10) < 0) {
        std::cerr << "监听失败" << std::endl;
        close(serverSocket);
        return 1;
    }

    std::cout << "进度接收服务器已启动，监听端口: " << port << std::endl;
    std::cout << "按Ctrl+C退出" << std::endl;

    // 启动状态监控线程
    std::thread monitor(statusMonitorThread);

    // 接收连接
    while (running) {
        // 设置超时，以便定期检查running标志
        fd_set readfds;
        FD_ZERO(&readfds);
        FD_SET(serverSocket, &readfds);

        struct timeval timeout;
        timeout.tv_sec = 1;
        timeout.tv_usec = 0;

        int activity = select(serverSocket + 1, &readfds, NULL, NULL, &timeout);

        if (activity < 0 && errno != EINTR) {
            std::cerr << "select错误: " << strerror(errno) << std::endl;
            writeLog("select错误: " + std::string(strerror(errno)));
            break;
        }

        // 如果没有活动，继续循环
        if (activity <= 0) {
            continue;
        }

        // 接受新连接
        struct sockaddr_in clientAddr;
        socklen_t clientAddrLen = sizeof(clientAddr);
        int clientSocket = accept(serverSocket, (struct sockaddr*)&clientAddr, &clientAddrLen);

        if (clientSocket < 0) {
            std::cerr << "接受连接失败: " << strerror(errno) << std::endl;
            writeLog("接受连接失败: " + std::string(strerror(errno)));
            continue;
        }

        // 获取客户端IP地址
        char clientIP[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &(clientAddr.sin_addr), clientIP, INET_ADDRSTRLEN);
        std::cout << "接收到来自 " << clientIP << " 的连接" << std::endl;
        writeLog("接收到来自 " + std::string(clientIP) + " 的连接");

        // 处理客户端连接
        handleClient(clientSocket);
    }

    // 等待监控线程结束
    if (monitor.joinable()) {
        monitor.join();
    }

    // 关闭服务器套接字
    close(serverSocket);
    std::cout << "服务器已关闭" << std::endl;
    writeLog("服务器已关闭");

    // 关闭日志文件
    if (enableLogging && logFile.is_open()) {
        logFile.close();
    }

    return 0;
}
