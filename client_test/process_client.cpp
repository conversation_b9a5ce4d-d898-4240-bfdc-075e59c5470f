#include <iostream>
#include <string>
#include <cstring>
#include <thread>
#include <atomic>
#include <chrono>
#include <mutex>
#include <queue>
#include <fstream>
#include <iomanip>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <signal.h>
#include <fcntl.h>
#include <errno.h>
#include <nlohmann/json.hpp>
#include <boost/program_options.hpp>

namespace po = boost::program_options;

// 全局变量
std::atomic<bool> running(true);
std::atomic<int> totalProcessed(0);
std::atomic<int> totalImages(0);
std::mutex logMutex;
std::ofstream logFile;
bool enableLogging = false;

// 信号处理函数
void signalHandler(int signum) {
    std::cout << "接收到信号 " << signum << "，准备退出..." << std::endl;
    running = false;
}

// 获取当前时间字符串
std::string getCurrentTimeString() {
    auto now = std::chrono::system_clock::now();
    auto now_time_t = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&now_time_t), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

// 写入日志
void writeLog(const std::string& message) {
    if (!enableLogging) return;
    
    std::lock_guard<std::mutex> lock(logMutex);
    logFile << "[" << getCurrentTimeString() << "] " << message << std::endl;
    logFile.flush();
}

// 发送处理请求
bool sendProcessRequest(const std::string& serverHost, int serverPort, 
                       const std::string& inputDir, const std::string& outputDir) {
    // 创建套接字
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock < 0) {
        std::cerr << "创建套接字失败" << std::endl;
        return false;
    }
    
    // 设置服务器地址
    struct sockaddr_in serv_addr;
    memset(&serv_addr, 0, sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(serverPort);
    
    // 转换IP地址
    if (inet_pton(AF_INET, serverHost.c_str(), &serv_addr.sin_addr) <= 0) {
        std::cerr << "无效的IP地址: " << serverHost << std::endl;
        close(sock);
        return false;
    }
    
    // 连接到服务器
    if (connect(sock, (struct sockaddr *)&serv_addr, sizeof(serv_addr)) < 0) {
        std::cerr << "连接到服务器失败: " << serverHost << ":" << serverPort << std::endl;
        close(sock);
        return false;
    }
    
    // 构建请求
    nlohmann::json request;
    request["type"] = "process_images";
    if (!inputDir.empty()) {
        request["input_dir"] = inputDir;
    }
    if (!outputDir.empty()) {
        request["output_dir"] = outputDir;
    }
    
    std::string requestStr = request.dump();
    
    // 发送请求
    if (send(sock, requestStr.c_str(), requestStr.length(), 0) < 0) {
        std::cerr << "发送请求失败" << std::endl;
        close(sock);
        return false;
    }
    
    std::cout << "已发送处理请求" << std::endl;
    writeLog("已发送处理请求: " + requestStr);
    
    // 接收响应
    const int bufferSize = 4096;
    char buffer[bufferSize];
    
    // 设置接收超时
    struct timeval timeout;
    timeout.tv_sec = 5;  // 5秒超时
    timeout.tv_usec = 0;
    if (setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
        std::cerr << "设置接收超时失败" << std::endl;
        close(sock);
        return false;
    }
    
    // 接收初始响应
    int bytesRead = recv(sock, buffer, bufferSize - 1, 0);
    if (bytesRead <= 0) {
        std::cerr << "接收响应失败" << std::endl;
        close(sock);
        return false;
    }
    
    // 确保字符串以null结尾
    buffer[bytesRead] = '\0';
    
    // 解析响应
    try {
        nlohmann::json response = nlohmann::json::parse(buffer);
        std::cout << "服务器响应: " << response["message"] << std::endl;
        writeLog("服务器响应: " + response["message"].get<std::string>());
        
        if (!response["success"].get<bool>()) {
            close(sock);
            return false;
        }
    } catch (const std::exception& e) {
        std::cerr << "解析响应失败: " << e.what() << std::endl;
        close(sock);
        return false;
    }
    
    // 接收进度更新和完成通知
    while (running) {
        // 接收数据
        bytesRead = recv(sock, buffer, bufferSize - 1, 0);
        if (bytesRead <= 0) {
            if (bytesRead == 0) {
                std::cout << "服务器关闭连接" << std::endl;
                writeLog("服务器关闭连接");
            } else if (errno == EAGAIN || errno == EWOULDBLOCK) {
                // 超时，继续等待
                continue;
            } else {
                std::cerr << "接收数据失败: " << strerror(errno) << std::endl;
                writeLog("接收数据失败: " + std::string(strerror(errno)));
            }
            break;
        }
        
        // 确保字符串以null结尾
        buffer[bytesRead] = '\0';
        
        // 解析消息
        try {
            nlohmann::json message = nlohmann::json::parse(buffer);
            
            if (message.contains("type")) {
                std::string type = message["type"];
                
                if (type == "progress") {
                    // 进度更新
                    int processed = message["processed"];
                    int total = message["total"];
                    totalProcessed = processed;
                    totalImages = total;
                    
                    // 计算百分比
                    double percentage = (total > 0) ? (processed * 100.0 / total) : 0.0;
                    
                    // 打印进度
                    std::cout << "\r进度: " << processed << "/" << total 
                              << " (" << std::fixed << std::setprecision(2) << percentage << "%)";
                    std::cout.flush();
                    
                    // 写入日志
                    std::stringstream progressMsg;
                    progressMsg << "进度: " << processed << "/" << total 
                               << " (" << std::fixed << std::setprecision(2) << percentage << "%)";
                    writeLog(progressMsg.str());
                } else if (type == "completion") {
                    // 完成通知
                    bool success = message["success"];
                    std::string msg = message["message"];
                    
                    std::cout << std::endl << "处理" << (success ? "成功" : "失败") << ": " << msg << std::endl;
                    writeLog("处理" + std::string(success ? "成功" : "失败") + ": " + msg);
                    
                    // 处理完成，退出循环
                    break;
                }
            }
        } catch (const std::exception& e) {
            std::cerr << "解析消息失败: " << e.what() << std::endl;
            writeLog("解析消息失败: " + std::string(e.what()));
        }
    }
    
    close(sock);
    return true;
}

// 查询服务器状态
bool queryServerStatus(const std::string& serverHost, int serverPort) {
    // 创建套接字
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock < 0) {
        std::cerr << "创建套接字失败" << std::endl;
        return false;
    }
    
    // 设置服务器地址
    struct sockaddr_in serv_addr;
    memset(&serv_addr, 0, sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(serverPort);
    
    // 转换IP地址
    if (inet_pton(AF_INET, serverHost.c_str(), &serv_addr.sin_addr) <= 0) {
        std::cerr << "无效的IP地址: " << serverHost << std::endl;
        close(sock);
        return false;
    }
    
    // 连接到服务器
    if (connect(sock, (struct sockaddr *)&serv_addr, sizeof(serv_addr)) < 0) {
        std::cerr << "连接到服务器失败: " << serverHost << ":" << serverPort << std::endl;
        close(sock);
        return false;
    }
    
    // 构建请求
    nlohmann::json request;
    request["type"] = "status";
    
    std::string requestStr = request.dump();
    
    // 发送请求
    if (send(sock, requestStr.c_str(), requestStr.length(), 0) < 0) {
        std::cerr << "发送请求失败" << std::endl;
        close(sock);
        return false;
    }
    
    // 接收响应
    const int bufferSize = 4096;
    char buffer[bufferSize];
    
    // 设置接收超时
    struct timeval timeout;
    timeout.tv_sec = 5;  // 5秒超时
    timeout.tv_usec = 0;
    if (setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
        std::cerr << "设置接收超时失败" << std::endl;
        close(sock);
        return false;
    }
    
    // 接收响应
    int bytesRead = recv(sock, buffer, bufferSize - 1, 0);
    if (bytesRead <= 0) {
        std::cerr << "接收响应失败" << std::endl;
        close(sock);
        return false;
    }
    
    // 确保字符串以null结尾
    buffer[bytesRead] = '\0';
    
    // 解析响应
    try {
        nlohmann::json response = nlohmann::json::parse(buffer);
        std::cout << "服务器状态: " << response["message"] << std::endl;
        std::cout << "是否正在处理: " << (response["processing"].get<bool>() ? "是" : "否") << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "解析响应失败: " << e.what() << std::endl;
        close(sock);
        return false;
    }
    
    close(sock);
    return true;
}

int main(int argc, char* argv[]) {
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    // 默认参数
    std::string serverHost = "127.0.0.1";
    int serverPort = 9000;
    std::string inputDir = "";
    std::string outputDir = "";
    std::string logFilePath = "";
    bool queryStatus = false;
    
    // 解析命令行参数
    try {
        po::options_description desc("允许的选项");
        desc.add_options()
            ("help", "显示帮助信息")
            ("host", po::value<std::string>(&serverHost)->default_value("127.0.0.1"), "服务器主机")
            ("port", po::value<int>(&serverPort)->default_value(9000), "服务器端口")
            ("input-dir", po::value<std::string>(&inputDir), "输入目录")
            ("output-dir", po::value<std::string>(&outputDir), "输出目录")
            ("log", po::value<std::string>(&logFilePath), "日志文件")
            ("status", po::bool_switch(&queryStatus), "查询服务器状态");
        
        po::variables_map vm;
        po::store(po::parse_command_line(argc, argv, desc), vm);
        po::notify(vm);
        
        if (vm.count("help")) {
            std::cout << desc << std::endl;
            return 0;
        }
        
        // 初始化日志
        if (vm.count("log")) {
            enableLogging = true;
            logFile.open(logFilePath, std::ios::app);
            if (!logFile.is_open()) {
                std::cerr << "无法打开日志文件: " << logFilePath << std::endl;
                enableLogging = false;
            } else {
                writeLog("客户端启动");
            }
        }
        
        // 执行操作
        if (queryStatus) {
            // 查询服务器状态
            if (!queryServerStatus(serverHost, serverPort)) {
                return 1;
            }
        } else {
            // 发送处理请求
            if (!sendProcessRequest(serverHost, serverPort, inputDir, outputDir)) {
                return 1;
            }
        }
        
        // 关闭日志文件
        if (enableLogging && logFile.is_open()) {
            writeLog("客户端退出");
            logFile.close();
        }
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
}
