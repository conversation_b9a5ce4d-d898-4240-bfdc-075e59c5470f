# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/workspace/maxi/client_test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/workspace/maxi/client_test/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/progress_receiver.dir/all
all: CMakeFiles/process_client.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/progress_receiver.dir/clean
clean: CMakeFiles/process_client.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/progress_receiver.dir

# All Build rule for target.
CMakeFiles/progress_receiver.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/progress_receiver.dir/build.make CMakeFiles/progress_receiver.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/progress_receiver.dir/build.make CMakeFiles/progress_receiver.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/workspace/maxi/client_test/build/CMakeFiles --progress-num=3,4 "Built target progress_receiver"
.PHONY : CMakeFiles/progress_receiver.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/progress_receiver.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/client_test/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/progress_receiver.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/client_test/build/CMakeFiles 0
.PHONY : CMakeFiles/progress_receiver.dir/rule

# Convenience name for target.
progress_receiver: CMakeFiles/progress_receiver.dir/rule
.PHONY : progress_receiver

# clean rule for target.
CMakeFiles/progress_receiver.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/progress_receiver.dir/build.make CMakeFiles/progress_receiver.dir/clean
.PHONY : CMakeFiles/progress_receiver.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/process_client.dir

# All Build rule for target.
CMakeFiles/process_client.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/process_client.dir/build.make CMakeFiles/process_client.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/process_client.dir/build.make CMakeFiles/process_client.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/workspace/maxi/client_test/build/CMakeFiles --progress-num=1,2 "Built target process_client"
.PHONY : CMakeFiles/process_client.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/process_client.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/client_test/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/process_client.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/maxi/client_test/build/CMakeFiles 0
.PHONY : CMakeFiles/process_client.dir/rule

# Convenience name for target.
process_client: CMakeFiles/process_client.dir/rule
.PHONY : process_client

# clean rule for target.
CMakeFiles/process_client.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/process_client.dir/build.make CMakeFiles/process_client.dir/clean
.PHONY : CMakeFiles/process_client.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

