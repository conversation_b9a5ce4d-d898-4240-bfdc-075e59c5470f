# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/workspace/maxi/client_test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/workspace/maxi/client_test/build

# Include any dependencies generated for this target.
include CMakeFiles/progress_receiver.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/progress_receiver.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/progress_receiver.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/progress_receiver.dir/flags.make

CMakeFiles/progress_receiver.dir/progress_receiver.cpp.o: CMakeFiles/progress_receiver.dir/flags.make
CMakeFiles/progress_receiver.dir/progress_receiver.cpp.o: /home/<USER>/workspace/maxi/client_test/progress_receiver.cpp
CMakeFiles/progress_receiver.dir/progress_receiver.cpp.o: CMakeFiles/progress_receiver.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/client_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/progress_receiver.dir/progress_receiver.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/progress_receiver.dir/progress_receiver.cpp.o -MF CMakeFiles/progress_receiver.dir/progress_receiver.cpp.o.d -o CMakeFiles/progress_receiver.dir/progress_receiver.cpp.o -c /home/<USER>/workspace/maxi/client_test/progress_receiver.cpp

CMakeFiles/progress_receiver.dir/progress_receiver.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/progress_receiver.dir/progress_receiver.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/client_test/progress_receiver.cpp > CMakeFiles/progress_receiver.dir/progress_receiver.cpp.i

CMakeFiles/progress_receiver.dir/progress_receiver.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/progress_receiver.dir/progress_receiver.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/client_test/progress_receiver.cpp -o CMakeFiles/progress_receiver.dir/progress_receiver.cpp.s

# Object files for target progress_receiver
progress_receiver_OBJECTS = \
"CMakeFiles/progress_receiver.dir/progress_receiver.cpp.o"

# External object files for target progress_receiver
progress_receiver_EXTERNAL_OBJECTS =

progress_receiver: CMakeFiles/progress_receiver.dir/progress_receiver.cpp.o
progress_receiver: CMakeFiles/progress_receiver.dir/build.make
progress_receiver: CMakeFiles/progress_receiver.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/workspace/maxi/client_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable progress_receiver"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/progress_receiver.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/progress_receiver.dir/build: progress_receiver
.PHONY : CMakeFiles/progress_receiver.dir/build

CMakeFiles/progress_receiver.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/progress_receiver.dir/cmake_clean.cmake
.PHONY : CMakeFiles/progress_receiver.dir/clean

CMakeFiles/progress_receiver.dir/depend:
	cd /home/<USER>/workspace/maxi/client_test/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/workspace/maxi/client_test /home/<USER>/workspace/maxi/client_test /home/<USER>/workspace/maxi/client_test/build /home/<USER>/workspace/maxi/client_test/build /home/<USER>/workspace/maxi/client_test/build/CMakeFiles/progress_receiver.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/progress_receiver.dir/depend

