# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/workspace/maxi/client_test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/workspace/maxi/client_test/build

# Include any dependencies generated for this target.
include CMakeFiles/process_client.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/process_client.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/process_client.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/process_client.dir/flags.make

CMakeFiles/process_client.dir/process_client.cpp.o: CMakeFiles/process_client.dir/flags.make
CMakeFiles/process_client.dir/process_client.cpp.o: /home/<USER>/workspace/maxi/client_test/process_client.cpp
CMakeFiles/process_client.dir/process_client.cpp.o: CMakeFiles/process_client.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/maxi/client_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/process_client.dir/process_client.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/process_client.dir/process_client.cpp.o -MF CMakeFiles/process_client.dir/process_client.cpp.o.d -o CMakeFiles/process_client.dir/process_client.cpp.o -c /home/<USER>/workspace/maxi/client_test/process_client.cpp

CMakeFiles/process_client.dir/process_client.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/process_client.dir/process_client.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/maxi/client_test/process_client.cpp > CMakeFiles/process_client.dir/process_client.cpp.i

CMakeFiles/process_client.dir/process_client.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/process_client.dir/process_client.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/maxi/client_test/process_client.cpp -o CMakeFiles/process_client.dir/process_client.cpp.s

# Object files for target process_client
process_client_OBJECTS = \
"CMakeFiles/process_client.dir/process_client.cpp.o"

# External object files for target process_client
process_client_EXTERNAL_OBJECTS =

process_client: CMakeFiles/process_client.dir/process_client.cpp.o
process_client: CMakeFiles/process_client.dir/build.make
process_client: /usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0
process_client: CMakeFiles/process_client.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/workspace/maxi/client_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable process_client"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/process_client.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/process_client.dir/build: process_client
.PHONY : CMakeFiles/process_client.dir/build

CMakeFiles/process_client.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/process_client.dir/cmake_clean.cmake
.PHONY : CMakeFiles/process_client.dir/clean

CMakeFiles/process_client.dir/depend:
	cd /home/<USER>/workspace/maxi/client_test/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/workspace/maxi/client_test /home/<USER>/workspace/maxi/client_test /home/<USER>/workspace/maxi/client_test/build /home/<USER>/workspace/maxi/client_test/build /home/<USER>/workspace/maxi/client_test/build/CMakeFiles/process_client.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/process_client.dir/depend

