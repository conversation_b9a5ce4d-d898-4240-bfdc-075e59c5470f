#include "detect/memory.hpp"

namespace yolo {

BaseMemory::BaseMemory(void *cpu,
                       size_t cpu_bytes,
                       void *gpu,
                       size_t gpu_bytes) {

  reference(cpu, cpu_bytes, gpu, gpu_bytes);
}

BaseMemory::~BaseMemory() {
    release();
}

void BaseMemory::reference(void *cpu,
                           size_t cpu_bytes,
                           void *gpu,
                           size_t gpu_bytes) {
    release();

    if (cpu == nullptr || cpu_bytes == 0) {
        cpu = nullptr;
        cpu_bytes = 0;
    }

    if (gpu == nullptr || gpu_bytes == 0) {
        gpu = nullptr;
        gpu_bytes = 0;
    }

    cpu_ = cpu;
    gpu_ = gpu;

    cpu_bytes_ = cpu_bytes;
    cpu_capacity_ = cpu_bytes;

    gpu_bytes_ = gpu_bytes;
    gpu_capacity_ = gpu_bytes;

    owner_cpu_ = !(cpu && cpu_bytes > 0);
    owner_gpu_ = !(gpu && gpu_bytes > 0);
}

void *BaseMemory::gpu_realloc(size_t bytes) {
    if (gpu_capacity_ < bytes) {
        // 先释放旧的内存
        release_gpu();

        // 获取当前设备
        int current_device;
        cudaGetDevice(&current_device);

        // 记录当前设备，以便在分配后恢复
        printf("Current GPU device in gpu_realloc: %d\n", current_device);

        // 设置容量
        gpu_capacity_ = bytes;

        // 尝试分配内存
        cudaError_t error = cudaMalloc(&gpu_, bytes);

        if (error != cudaSuccess) {
            // 如果分配失败，打印错误信息
            printf("Error: Failed to allocate GPU memory on device %d: %s\n",
                   current_device, cudaGetErrorString(error));

            // 重置容量和指针
            gpu_capacity_ = 0;
            gpu_ = nullptr;

            // 尝试在设备上同步，确保所有操作完成
            cudaDeviceSynchronize();
        } else {
            printf("Successfully allocated GPU memory on device %d\n", current_device);

            // 初始化内存为零，避免未初始化内存导致的问题
            cudaMemset(gpu_, 0, bytes);
        }
    }

    // 更新字节数
    if (gpu_ != nullptr) {
        gpu_bytes_ = bytes;
    } else {
        gpu_bytes_ = 0;
    }

    return gpu_;
}

void *BaseMemory::cpu_realloc(size_t bytes) {
    if (cpu_capacity_ < bytes) {
        release_cpu();

        cpu_capacity_ = bytes;
        checkRuntime(cudaMallocHost(&cpu_, bytes));
        Assert(cpu_ != nullptr);
    }

    cpu_bytes_ = bytes;
    return cpu_;
}
// (void *) 0x7fff49c00200; cpu_capacity_=526336
void BaseMemory::release_cpu() {
    if(cpu_) {
        if(owner_cpu_) {
            checkRuntime(cudaFreeHost(cpu_));
        }
        cpu_ = nullptr;
    }

    cpu_capacity_=0;
    cpu_bytes_=0;
}

void BaseMemory::release_gpu() {
    if (gpu_) {
        if (owner_gpu_) {
            checkRuntime(cudaFree(gpu_));
        }
        gpu_ = nullptr;
    }

    gpu_capacity_ = 0;
    gpu_bytes_ = 0;
}

void BaseMemory::release() {
    release_cpu();
    release_gpu();
}

} // namespace yolo
