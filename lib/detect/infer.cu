#include "detect/infer.hpp"

namespace trt {

using namespace nvinfer1;

NvLogger gLogger;
bool NvContext::construct(const void *pdata, size_t size) {
    __destroy();

    if(pdata==nullptr || size <= 0) return false;
    runtime_ = std::shared_ptr<IRuntime>(createInferRuntime(gLogger),
                                         __destroy_nvidia_pointer<IRuntime>);
    if(runtime_==nullptr) return false;

    engine_ = std::shared_ptr<ICudaEngine>(
        runtime_->deserializeCudaEngine(pdata, size, nullptr),
        __destroy_nvidia_pointer<ICudaEngine>);

    if(engine_==nullptr) return false;

    context_ = std::shared_ptr<IExecutionContext>(
        engine_->createExecutionContext(),
        __destroy_nvidia_pointer<IExecutionContext>);

    return context_ != nullptr;
}

/*
TrTImpl::TrTImpl() {
    __logger(log4cplus::Logger::getInstance(LOG4CPLUS_TEXT("TrTImpl"))) {}
*/

bool TrTImpl::construct(const void *pdata, size_t size) {
     nv_context = std::make_shared<NvContext>();
     if(!nv_context->construct(pdata, size)) return false;

     setup();
     return true;
}

void TrTImpl::setup() {
    auto engine = this->nv_context->engine_;
    int nbBindings = engine->getNbBindings();

    binding_name_to_index_.clear();

    /* get input and output names */
    for(int i=0; i<nbBindings; ++i) {
        const char *BingdingName = engine->getBindingName(i);
        binding_name_to_index_[BingdingName] = i;
    }
}

bool TrTImpl::load(const std::string &file) {
    auto data = load_file(file);
    if (data.empty()) {
        _INFO("An empty file has been loaded. Please confirm your file path: %s",
            file.c_str());
        //LOG4CPLUS_ERROR(__logger, "An empty file has been loaded. Please confirm your file path:" << file);
        return false;
    }

    return this->construct(data.data(), data.size());
}

int TrTImpl::index(const std::string &name) {
    auto iter = binding_name_to_index_.find(name);
    Assertf(iter != binding_name_to_index_.end(),
            "Can not found the binding name: %s",
            name.c_str());

    return iter->second;
}

bool TrTImpl::forward(const std::vector<void *> &bindings,
                        void *stream,
                        void *input_consum_event) {

    // 获取当前设备
    int current_device;
    cudaGetDevice(&current_device);
    printf("TrTImpl::forward 在设备 %d 上执行\n", current_device);

    // 检查流是否有效
    if (stream == nullptr) {
        printf("错误: TrTImpl::forward 中的 CUDA 流为空\n");
        return false;
    }

    // 检查绑定是否有效
    for (size_t i = 0; i < bindings.size(); ++i) {
        if (bindings[i] == nullptr) {
            printf("错误: TrTImpl::forward 中的绑定 %zu 为空\n", i);
            return false;
        }
    }

    // 尝试执行推理
    bool success = false;
    try {
        success = this->nv_context->context_->enqueueV2((void**)bindings.data(),
                                    (cudaStream_t)stream,
                                    (cudaEvent_t *)input_consum_event);

        if (!success) {
            printf("错误: TrTImpl::forward 中的 enqueueV2 调用失败\n");
        } else {
            printf("TrTImpl::forward 成功执行\n");
        }
    } catch (const std::exception& e) {
        printf("错误: TrTImpl::forward 中捕获到异常: %s\n", e.what());
        success = false;
    } catch (...) {
        printf("错误: TrTImpl::forward 中捕获到未知异常\n");
        success = false;
    }

    return success;
}

std::vector<int> TrTImpl::dynamic_dims(const std::string &name) {
    return dynamic_dims(index(name));
}

std::vector<int> TrTImpl::dynamic_dims(int ibinding) {
    auto dim = this->nv_context->context_->getBindingDimensions(ibinding);
    return std::vector<int>(dim.d, dim.d + dim.nbDims);
}

std::vector<int> TrTImpl::static_dims(const std::string &name) {
    return static_dims(index(name));
}

std::vector<int> TrTImpl::static_dims(int ibinding) {
    auto dim = this->nv_context->engine_->getBindingDimensions(ibinding);
    return std::vector<int>(dim.d, dim.d + dim.nbDims);
}

int TrTImpl::num_bindings() {
    return this->nv_context->engine_->getNbBindings();
}

bool TrTImpl::is_input(int ibinding) {
    return this->nv_context->engine_->bindingIsInput(ibinding);
}

bool TrTImpl::set_dynamic_dims(const std::string &name, const std::vector<int> &dims) {
    return this->set_dynamic_dims(index(name), dims);
}

bool TrTImpl::set_dynamic_dims(int ibinding, const std::vector<int> &dims) {
    Dims d;
    memcpy(d.d, dims.data(), sizeof(int) * dims.size());
    d.nbDims = dims.size();
    return this->nv_context->context_->setBindingDimensions(ibinding, d);
}

int TrTImpl::numel(const std::string &name) {
    return numel(index(name));
}

int TrTImpl::numel(int ibinding) {
    auto dim = this->nv_context->context_->getBindingDimensions(ibinding);
    return std::accumulate(dim.d, dim.d + dim.nbDims, 1, std::multiplies<int>());
}

DType TrTImpl::dtype(const std::string &name) {
    return dtype(index(name));
}

DType TrTImpl::dtype(int ibinding) {
    return static_cast<DType>(this->nv_context->engine_->getBindingDataType(ibinding));
}

bool TrTImpl::has_dynamic_dim() {
    // check if any input or output bindings have dynamic shapes
    int numBindings = this->nv_context->engine_->getNbBindings();
    for (int i = 0; i < numBindings; ++i) {
      nvinfer1::Dims dims = this->nv_context->engine_->getBindingDimensions(i);
      for (int j = 0; j < dims.nbDims; ++j) {
        if (dims.d[j] == -1) return true;
      }
    }

    return false;
}

void TrTImpl::print() {
    _INFO("TrTInfer %p [%s]", this, has_dynamic_dim() ? "DynamicShape" : "StaticShape");
    //LOG4CPLUS_TRACE(__logger, "shape: " <<
    //                (has_dynamic_dim() ? "DynamicShape" : "StaticShape"));

    int num_input = 0;
    int num_output = 0;
    auto engine = this->nv_context->engine_;
    for (int i = 0; i < engine->getNbBindings(); ++i) {
      if (engine->bindingIsInput(i))
        num_input++;
      else
        num_output++;
    }

    //_INFO("Inputs: %d", num_input);
    //LOG4CPLUS_TRACE(__logger, "inputs: " << num_input);

    for (int i = 0; i < num_input; ++i) {
        auto name = engine->getBindingName(i);
        auto dim = engine->getBindingDimensions(i);
        _INFO("\t%d.%s : shape {%s}", i, name, format_shape(dim).c_str());
        //LOG4CPLUS_TRACE(__logger, "\t" << i << "." << name << format_shape(dim));
    }

    _INFO("Outputs: %d", num_output);
    //LOG4CPLUS_TRACE(__logger, "outputs:" << num_output);
    for (int i = 0; i < num_output; ++i) {
        auto name = engine->getBindingName(i + num_input);
        auto dim = engine->getBindingDimensions(i + num_input);
        _INFO("\t%d.%s : shape {%s}", i, name, format_shape(dim).c_str());
        //LOG4CPLUS_TRACE(__logger, "\t" << i << "." << name << format_shape(dim));
    }
}

std::string TrTImpl::format_shape(const Dims &shape) {
    std::stringstream output;

    char buf[64];
    const char *fmts[] = {"%d", "x%d"};
    for (int i = 0; i < shape.nbDims; ++i) {
      snprintf(buf, sizeof(buf), fmts[i != 0], shape.d[i]);
      output << buf;
    }
    return output.str();
}

std::shared_ptr<TrTInfer> load(const std::string &file) {
    std::shared_ptr<TrTImpl> impl = std::make_shared<TrTImpl>();
    if (!impl->load(file)) {
      impl.reset();
    }

    return impl;
}

} // namespace trt
