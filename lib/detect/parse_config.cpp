#include "detect/parse_config.hpp"
#include <iostream>
#include <filesystem>

namespace yolo {

void ParseConf::__parse_config(const std::string &config_file) {
    // 检查文件是否存在
    if (!std::filesystem::exists(config_file)) {
        std::cerr << "Config file not found: " << config_file << std::endl;
        success = false;
        return;
    }

    // 检查文件扩展名
    std::string extension = std::filesystem::path(config_file).extension().string();

    // 加载配置文件
    if (extension == ".json") {
        // 使用 JSON 配置解析器
        if (!json_parser.loadConfig(config_file)) {
            std::cerr << "Failed to load JSON config file: " << config_file << std::endl;
            success = false;
            return;
        }

        // 获取配置
        const auto& config = json_parser.getConfig();

        // 复制配置到会话结构体
        task_session.classes = config.task.classes;
        task_session.num_classes = config.task.num_classes;
        task_session.debug_log = config.task.debug_log;
        task_session.err_log = config.task.err_log;

        pre_session.visualize_affine = config.preprocess.visualize_affine;
        pre_session.show_info = config.preprocess.show_info;
        pre_session.max_batch_size = config.preprocess.max_batch_size;

        post_session.confidence_threshold = config.postprocess.confidence_threshold;
        post_session.nms_threshold = config.postprocess.nms_threshold;
        post_session.num_box_element = config.postprocess.num_box_element;
        post_session.max_image_bboxes = config.postprocess.max_image_bboxes;

        gpu_session.gpu_block_threads = config.gpu.gpu_block_threads;
        gpu_session.max_block_size = config.gpu.max_block_size;
        gpu_session.mem_align = config.gpu.mem_align;
        gpu_session.gpu_id = config.gpu.gpu_id;

        cpu_session.timer = config.multi_threads.timer;
        cpu_session.max_queue_imgs = config.multi_threads.max_queue_imgs;
        cpu_session.micro_sec_laytency = config.multi_threads.micro_sec_laytency;
    } else {
        // 不支持的文件格式
        std::cerr << "Unsupported config file format: " << extension << std::endl;
        std::cerr << "Please use .json format for configuration files." << std::endl;
        success = false;
        return;
    }
}

} // namespace yolo
