#include "mpf/message.h"

namespace mpf {

std::string serializeMessage(const ProcessMessage& msg) {
    nlohmann::json j;
    j["type"] = static_cast<int>(msg.type);
    j["process_id"] = msg.process_id;
    j["data"] = msg.data;
    j["count"] = msg.count;
    return j.dump();
}

ProcessMessage deserializeMessage(const std::string& jsonStr) {
    ProcessMessage msg;
    nlohmann::json j = nlohmann::json::parse(jsonStr);
    msg.type = static_cast<ProcessMessage::Type>(j["type"].get<int>());
    msg.process_id = j["process_id"].get<int>();
    msg.data = j["data"].get<std::string>();
    msg.count = j["count"].get<int>();
    return msg;
}

} // namespace mpf
