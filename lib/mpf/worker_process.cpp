#include "mpf/worker_process.h"
#include "mpf/logger.h"
#include "mpf/message.h"
#include "mpf/task_manager.h"
#include <nlohmann/json.hpp>

// 声明 setYoloConfig 函数
namespace mpf {
extern void setYoloConfig(const std::string& engine_file,
                        const std::string& config_file,
                        const std::string& task_name,
                        const std::string& log_file);
}

namespace mpf {

WorkerProcess::WorkerProcess(int processId, const std::string& outputDir,
                           const std::string& zmqEndpoint, const std::string& logFile)
    : processId_(processId),
      outputDir_(outputDir),
      zmqEndpoint_(zmqEndpoint),
      logFile_(logFile),
      context_(1),
      socket_(context_, zmq::socket_type::dealer) {

    // 初始化日志
    if (!Logger::initWorkerLogger(processId_, logFile_)) {
        throw std::runtime_error("无法初始化日志系统");
    }

    // 设置ZMQ身份标识
    std::string identity = std::to_string(processId_);
    socket_.setsockopt(ZMQ_IDENTITY, identity.c_str(), identity.size());
    socket_.connect(zmqEndpoint_);
}

int WorkerProcess::run() {
    spdlog::info("工作进程启动");

    // 等待任务
    std::vector<std::string> imagePaths;
    bool running = true;

    while (running) {
        zmq::message_t message;
        auto result = socket_.recv(message, zmq::recv_flags::none);
        if (!result) continue;

        std::string messageStr(static_cast<char*>(message.data()), message.size());
        ProcessMessage msg = deserializeMessage(messageStr);

        switch (msg.type) {
            case ProcessMessage::Type::TASK: {
                // 解析任务数据
                nlohmann::json taskData = nlohmann::json::parse(msg.data);
                imagePaths = taskData["image_paths"].get<std::vector<std::string>>();

                spdlog::info("收到 {} 个任务", imagePaths.size());

                // 处理图片
                int processedCount = processTasks(imagePaths);

                // 报告任务完成
                ProcessMessage completeMsg;
                completeMsg.type = ProcessMessage::Type::COMPLETE;
                completeMsg.process_id = processId_;
                completeMsg.count = processedCount;

                std::string serialized = serializeMessage(completeMsg);
                zmq::message_t completeMessage(serialized);
                socket_.send(completeMessage, zmq::send_flags::none);

                break;
            }

            case ProcessMessage::Type::TERMINATE:
                spdlog::info("收到终止信号");
                running = false;
                break;

            case ProcessMessage::Type::CONFIG:
                spdlog::info("收到 YOLOv8 配置信息");
                processYoloConfig(msg.data);
                break;

            default:
                spdlog::error("收到未知消息类型");
                break;
        }
    }

    spdlog::info("工作进程退出");
    return 0;
}

int WorkerProcess::processTasks(const std::vector<std::string>& imagePaths) {
    if (imagePaths.empty()) {
        spdlog::warn("没有图片需要处理");
        return 0;
    }

    spdlog::info("开始批量处理 {} 张图片", imagePaths.size());

    // 创建 TaskManager 对象
    TaskManager taskManager;

    // 使用 processBatch 方法批量处理图片，传入进程 ID 作为 GPU ID
    if (taskManager.processBatch(imagePaths, outputDir_, processId_)) {
        // 报告进度
        ProcessMessage progressMsg;
        progressMsg.type = ProcessMessage::Type::PROGRESS;
        progressMsg.process_id = processId_;
        progressMsg.count = imagePaths.size();

        std::string serialized = serializeMessage(progressMsg);
        zmq::message_t progressMessage(serialized);
        socket_.send(progressMessage, zmq::send_flags::none);

        spdlog::info("已处理 {}/{} 张图片", imagePaths.size(), imagePaths.size());
        return imagePaths.size();
    } else {
        spdlog::error("批量处理图片失败");
        return 0;
    }
}

void WorkerProcess::processYoloConfig(const std::string& configData) {
    try {
        // 解析配置数据
        nlohmann::json configJson = nlohmann::json::parse(configData);

        // 获取配置信息
        std::string engine_file = configJson["engine_file"].get<std::string>();
        std::string config_file = configJson["config_file"].get<std::string>();
        std::string task_name = configJson["task_name"].get<std::string>();
        std::string log_file = configJson["log_file"].get<std::string>();

        // 设置 YOLOv8 配置
        spdlog::info("设置 YOLOv8 配置...");
        spdlog::info("引擎文件: {}", engine_file);
        spdlog::info("配置文件: {}", config_file);
        spdlog::info("任务名称: {}", task_name);
        spdlog::info("日志文件: {}", log_file);

        // 调用 setYoloConfig 函数设置全局配置
        setYoloConfig(engine_file, config_file, task_name, log_file);

        spdlog::info("YOLOv8 配置已设置");
    } catch (const std::exception& e) {
        spdlog::error("处理 YOLOv8 配置时出错: {}", e.what());
    }
}

} // namespace mpf
