#include "mpf/task_manager.h"
#include "utils/logger.h"
#include "detect/test.hpp"
#include <opencv2/opencv.hpp>
#include <fstream>
#include <nlohmann/json.hpp>
#include <memory>

namespace mpf {

// YOLOv8 配置信息
struct YoloConfig {
    std::string engine_file;
    std::string config_file;
    std::string task_name;
    std::string log_file;
};

// 全局 YOLOv8 配置
static YoloConfig g_yolo_config;

// 设置 YOLOv8 配置
void setYoloConfig(const std::string& engine_file,
                  const std::string& config_file,
                  const std::string& task_name,
                  const std::string& log_file) {
    g_yolo_config.engine_file = engine_file;
    g_yolo_config.config_file = config_file;
    g_yolo_config.task_name = task_name;
    g_yolo_config.log_file = log_file;
    spdlog::info("YOLOv8 配置已设置");
    spdlog::info("引擎文件: {}", engine_file);
    spdlog::info("配置文件: {}", config_file);
    spdlog::info("任务名称: {}", task_name);
    spdlog::info("日志文件: {}", log_file);
}

std::vector<std::string> TaskManager::collectImagePaths(const std::string& baseDir) {
    std::vector<std::string> imagePaths;

    // 检查baseDir是否已经是T-L、T-M或T-R子目录
    std::string baseDirName = fs::path(baseDir).filename().string();
    bool isSubDir = (baseDirName == "T-L" || baseDirName == "T-M" || baseDirName == "T-R");

    if (isSubDir) {
        // 直接处理指定的子目录
        try {
            for (const auto& entry : fs::directory_iterator(baseDir)) {
                if (fs::is_regular_file(entry.path())) {
                    std::string ext = entry.path().extension().string();
                    // 只处理图片文件
                    if (ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".bmp") {
                        imagePaths.push_back(entry.path().string());
                    }
                }
            }
        } catch (const std::exception& e) {
            spdlog::error("读取目录 {} 时出错: {}", baseDir, e.what());
        }
    } else {
        // 处理所有子目录
        std::vector<std::string> subDirs = {"T-L", "T-M", "T-R"};

        for (const auto& subDir : subDirs) {
            std::string dirPath = baseDir + "/" + subDir;

            try {
                for (const auto& entry : fs::directory_iterator(dirPath)) {
                    if (fs::is_regular_file(entry.path())) {
                        std::string ext = entry.path().extension().string();
                        // 只处理图片文件
                        if (ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".bmp") {
                            imagePaths.push_back(entry.path().string());
                        }
                    }
                }
            } catch (const std::exception& e) {
                spdlog::error("读取目录 {} 时出错: {}", dirPath, e.what());
            }
        }
    }

    return imagePaths;
}

std::vector<std::vector<std::string>> TaskManager::distributeImagePaths(
    const std::vector<std::string>& imagePaths,
    int numProcesses
) {
    std::vector<std::vector<std::string>> processImagePaths(numProcesses);

    for (size_t i = 0; i < imagePaths.size(); ++i) {
        processImagePaths[i % numProcesses].push_back(imagePaths[i]);
    }

    return processImagePaths;
}

bool TaskManager::processImage(const std::string& imagePath, const std::string& outputDir) {
    try {
        // 获取图片文件名（不含扩展名）
        fs::path path(imagePath);
        std::string filename = path.stem().string();

        // 确定输出目录
        fs::path inputPath(imagePath);
        std::string parentDir = inputPath.parent_path().filename().string();
        std::string outputPath;

        // 检查parentDir是否是T-L、T-M或T-R
        if (parentDir == "T-L" || parentDir == "T-M" || parentDir == "T-R") {
            // 使用正确的子目录
            outputPath = outputDir + "/" + parentDir;
        } else {
            // 如果不是，使用默认目录
            outputPath = outputDir + "/default";
        }

        // 确保输出目录存在
        fs::create_directories(outputPath);

        // 使用 YOLOv8 推理
        spdlog::info("开始处理图片: {}", imagePath);

        // 获取当前进程的 GPU ID
        // 注意：这里假设环境变量 CUDA_VISIBLE_DEVICES 已经设置了可见的 GPU
        // 在这种情况下，进程看到的 GPU 总是 GPU 0
        int gpu_id = 0;

        // 创建 Test 对象
        spdlog::info("创建 YOLOv8 推理对象，使用 GPU {}", gpu_id);
        Test test(
            g_yolo_config.engine_file,
            g_yolo_config.config_file,
            g_yolo_config.task_name + "_" + std::to_string(gpu_id),
            g_yolo_config.log_file + "_gpu" + std::to_string(gpu_id) + ".txt",
            yolo::Type::V8Det
        );

        // 创建图片路径列表，只包含当前图片
        std::vector<std::string> image_paths = {imagePath};

        // 使用 thread_img_list 方法处理图片列表
        int result = test.thread_img_list(image_paths, outputPath, gpu_id);

        if (result != 0) {
            spdlog::error("YOLOv8 推理失败，错误码: {}", result);
            return false;
        }

        spdlog::info("图片处理完成: {}", imagePath);
        return true;
    } catch (const std::exception& e) {
        spdlog::error("处理图片 {} 时出错: {}", imagePath, e.what());
        return false;
    }
}

bool TaskManager::processBatch(const std::vector<std::string>& imagePaths, const std::string& outputDir, int gpuId) {
    try {
        if (imagePaths.empty()) {
            spdlog::warn("没有图片需要处理");
            return true;
        }

        // 按照子目录分组图片
        std::map<std::string, std::vector<std::string>> groupedImages;

        for (const auto& imagePath : imagePaths) {
            fs::path inputPath(imagePath);
            std::string parentDir = inputPath.parent_path().filename().string();

            // 检查parentDir是否是T-L、T-M或T-R
            if (parentDir == "T-L" || parentDir == "T-M" || parentDir == "T-R") {
                groupedImages[parentDir].push_back(imagePath);
            } else {
                groupedImages["default"].push_back(imagePath);
            }
        }

        // 使用传入的 GPU ID
        // 注意：这里假设环境变量 CUDA_VISIBLE_DEVICES 已经设置了可见的 GPU
        // 在这种情况下，进程看到的 GPU 总是 GPU 0，但我们仍然使用传入的 GPU ID 来区分不同的进程
        int gpu_id = gpuId;

        // 创建 Test 对象
        spdlog::info("创建 YOLOv8 推理对象，使用 GPU {}", gpu_id);
        spdlog::info("引擎文件: {}", g_yolo_config.engine_file);
        spdlog::info("配置文件: {}", g_yolo_config.config_file);
        spdlog::info("任务名称: {}", g_yolo_config.task_name + "_" + std::to_string(gpu_id));
        spdlog::info("日志文件: {}", g_yolo_config.log_file + "_gpu" + std::to_string(gpu_id) + ".txt");

        Test test(
            g_yolo_config.engine_file,
            g_yolo_config.config_file,
            g_yolo_config.task_name + "_" + std::to_string(gpu_id),
            g_yolo_config.log_file + "_gpu" + std::to_string(gpu_id) + ".txt",
            yolo::Type::V8Det
        );
        spdlog::info("YOLOv8 推理对象创建成功");

        // 对每个子目录的图片进行批量处理
        for (const auto& [subDir, subImages] : groupedImages) {
            // 确定输出目录
            std::string outputPath = outputDir + "/" + subDir;

            // 确保输出目录存在
            fs::create_directories(outputPath);

            // 使用 thread_img_list 方法处理图片列表
            spdlog::info("开始处理 {} 子目录的 {} 张图片", subDir, subImages.size());
            int result = test.thread_img_list(subImages, outputPath, gpu_id);

            if (result != 0) {
                spdlog::error("处理 {} 子目录的图片时出错，错误码: {}", subDir, result);
                return false;
            }

            spdlog::info("{} 子目录的图片处理完成", subDir);
        }

        spdlog::info("所有图片处理完成，共 {} 张", imagePaths.size());
        return true;
    } catch (const std::exception& e) {
        spdlog::error("批量处理图片时出错: {}", e.what());
        return false;
    } catch (...) {
        spdlog::error("批量处理图片时出现未知错误");
        return false;
    }
}

} // namespace mpf
