#include "mpf/main_process.h"
#include "mpf/logger.h"
#include "mpf/message.h"
#include "mpf/task_manager.h"
#include "mpf/gpu_detector.h"
#include "mpf/config_parser.h"
#include <mutex>
#include <iomanip>
#include <algorithm>
#include <nlohmann/json.hpp>

namespace mpf {

// 全局互斥锁，用于同步输出
std::mutex cout_mutex;

MainProcess::MainProcess(int numProcesses, const std::string& configFile,
                       const std::string& inputDir,
                       const std::string& outputDir, const std::string& logFile,
                       int batchSize, const std::string& windowsHost,
                       int windowsPort,
                       std::function<void(int, int)> progressCallback,
                       std::function<void(bool, const std::string&)> completionCallback,
                       const std::vector<int>& clientGpuIds)
    : numProcesses_(numProcesses),
      config_file_(configFile),
      inputDir_(inputDir),
      outputDir_(outputDir),
      logFile_(logFile),
      batchSize_(batchSize),
      context_(1),
      socket_(context_, zmq::socket_type::router),
      windowsHost_(windowsHost),
      windowsPort_(windowsPort),
      progressCallback_(progressCallback),
      completionCallback_(completionCallback),
      running_(true),
      totalProcessed_(0),
      totalImages_(0) {

    // 如果客户端指定了GPU ID，使用客户端指定的
    if (!clientGpuIds.empty()) {
        gpu_ids_ = clientGpuIds;
    }

    // 初始化日志
    if (!Logger::initMainLogger(logFile_)) {
        throw std::runtime_error("无法初始化日志系统");
    }

    // 初始化时间点
    startTime_ = std::chrono::high_resolution_clock::now();
    lastUpdateTime_ = startTime_;
    lastTotalProcessed_ = 0;
}

void MainProcess::stop() {
    running_ = false;

    // 停止Windows报告器
    if (windowsReporter_) {
        spdlog::info("正在停止Windows报告器...");
        windowsReporter_->stop();
    }

    // 终止所有工作进程
    spdlog::info("正在终止所有工作进程...");
    terminateWorkerProcesses();
}

void MainProcess::getProgress(int& processed, int& total) const {
    processed = totalProcessed_.load();
    total = totalImages_.load();
}

int MainProcess::run() {
    // 设置运行标志
    running_ = true;

    // 检测GPU
    int gpuCount = 0;
    std::string gpuInfo;
    if (GpuDetector::detectGpu(gpuCount, gpuInfo)) {
        spdlog::info("检测到 {} 个GPU", gpuCount);
        if (gpuCount > 0) {
            parse_gpu_ids();
            spdlog::info("GPU信息: {}", gpuInfo);

            // 如果有GPU ID，则根据GPU ID数量设置进程数
            if (!gpu_ids_.empty()) {
                numProcesses_ = static_cast<int>(gpu_ids_.size());
                std::string gpu_ids_str = "使用的 GPU IDs = ";
                for(auto& gpu_id: gpu_ids_)
                    gpu_ids_str += std::to_string(gpu_id) + " ";
                spdlog::info("{}", gpu_ids_str);
                spdlog::info("根据GPU数量设置进程数为 {}", numProcesses_);
            } else {
                spdlog::info("未指定GPU ID，使用默认进程数 {}", numProcesses_);
            }
        }
    } else {
        spdlog::warn("未检测到GPU或GPU检测失败");
    }

    spdlog::info("启动 {} 个工作进程", numProcesses_);
    spdlog::info("输入目录: {}", inputDir_);
    spdlog::info("输出目录: {}", outputDir_);
    spdlog::info("每批任务数量: {}", batchSize_);

    // 初始化Windows报告器
    if (!windowsHost_.empty() && windowsPort_ > 0) {
        spdlog::info("初始化Windows报告器，目标: {}:{}", windowsHost_, windowsPort_);
        // 设置重试参数：最大重试5次，重试间隔5秒，连接超时1秒
        windowsReporter_ = std::make_unique<WindowsReporter>(windowsHost_, windowsPort_, outputDir_, 5, 5000, 1000);
    }

    // 收集所有图片路径
    spdlog::info("正在收集图片路径...");
    std::vector<std::string> imagePaths = TaskManager::collectImagePaths(inputDir_);
    spdlog::info("找到 {} 张图片", imagePaths.size());

    if (imagePaths.empty()) {
        spdlog::error("输入目录中没有找到图片");
        if (completionCallback_) {
            completionCallback_(false, "输入目录中没有找到图片");
        }
        return 1;
    }

    // 设置总图片数量
    totalImages_ = imagePaths.size();

    // 设置Windows报告器的总图片数量并启动
    if (windowsReporter_) {
        windowsReporter_->setTotalImages(imagePaths.size());
        if (!windowsReporter_->start()) {
            spdlog::error("启动Windows报告器失败");
        }
    }

    // 将所有图片路径添加到任务队列
    for (const auto& path : imagePaths) {
        taskQueue_.push(path);
    }

    // 设置ZMQ通信
    socket_.bind("tcp://*:5555");

    // 启动子进程
    if (!startWorkerProcesses()) {
        spdlog::error("启动工作进程失败");
        return 1;
    }

    // 等待所有子进程连接
    std::this_thread::sleep_for(std::chrono::seconds(1));

    // 初始分发任务给子进程
    if (!initialDistributeTasks()) {
        spdlog::error("初始分发任务失败");
        return 1;
    }

    // 监控进度
    int totalProcessed = 0;

    try {
        // 设置超时时间，避免无限等待
        auto startMonitorTime = std::chrono::high_resolution_clock::now();
        const int MAX_MONITOR_SECONDS = 3600; // 最长监控1小时

        // 跟踪每个进程的进度
        std::vector<int> processProgress(numProcesses_, 0);
        std::vector<bool> processCompleted(numProcesses_, false);
        int completedProcesses = 0;

        // 接收子进程的进度报告
        while (running_ && totalProcessed < imagePaths.size() && completedProcesses < numProcesses_) {
            // 检查是否超时
            auto currentTime = std::chrono::high_resolution_clock::now();
            auto monitorSeconds = std::chrono::duration_cast<std::chrono::seconds>(
                currentTime - startMonitorTime).count();

            if (monitorSeconds > MAX_MONITOR_SECONDS) {
                spdlog::warn("监控时间超过最大限制，强制退出");
                break;
            }

            // 设置接收超时
            zmq::pollitem_t items[] = {
                { socket_, 0, ZMQ_POLLIN, 0 }
            };

            // 使用较短的超时时间
            zmq::poll(items, 1, std::chrono::milliseconds(500));

            // 如果没有消息
            if (!(items[0].revents & ZMQ_POLLIN)) {
                // 如果任务队列为空且所有图片都已处理完成，检查是否所有进程都已完成
                if (taskQueue_.empty() && totalProcessed >= imagePaths.size()) {
                    // 向所有未完成的进程发送终止信号
                    for (int i = 0; i < numProcesses_; ++i) {
                        if (!processCompleted[i]) {
                            ProcessMessage msg;
                            msg.type = ProcessMessage::Type::TERMINATE;
                            msg.process_id = i;

                            std::string serialized = serializeMessage(msg);
                            zmq::message_t identity(std::to_string(i));
                            socket_.send(identity, zmq::send_flags::sndmore);
                            zmq::message_t message(serialized);
                            socket_.send(message, zmq::send_flags::none);

                            processCompleted[i] = true;
                            completedProcesses++;
                            spdlog::info("已向进程 {} 发送终止信号", i);
                        }
                    }

                    // 如果所有进程都已完成，退出循环
                    if (completedProcesses >= numProcesses_) {
                        spdlog::info("所有进程已完成任务，退出监控循环");
                        break;
                    }
                }

                continue;
            }

            // 接收消息
            zmq::message_t identity;
            zmq::message_t message;

            auto result = socket_.recv(identity, zmq::recv_flags::none);
            if (!result) continue;

            result = socket_.recv(message, zmq::recv_flags::none);
            if (!result) continue;

            std::string identityStr(static_cast<char*>(identity.data()), identity.size());
            std::string messageStr(static_cast<char*>(message.data()), message.size());

            ProcessMessage msg = deserializeMessage(messageStr);
            int processId = msg.process_id;

            if (msg.type == ProcessMessage::Type::PROGRESS) {
                int count = msg.count;

                // 更新进度
                int newProgress = count - processProgress[processId];
                processProgress[processId] = count;
                totalProcessed_ += newProgress;
                totalProcessed = totalProcessed_.load();

                // 计算处理速度
                auto elapsedSeconds = std::chrono::duration_cast<std::chrono::seconds>(
                    currentTime - startTime_).count();
                auto timeSinceLastUpdate = std::chrono::duration_cast<std::chrono::milliseconds>(
                    currentTime - lastUpdateTime_).count() / 1000.0; // 转换为秒，保留小数部分

                // 计算总体平均速度（每秒处理图片数）
                double overallSpeed = (elapsedSeconds > 0) ?
                    static_cast<double>(totalProcessed) / elapsedSeconds : 0.0;

                // 计算当前速度（最近一次更新以来的每秒处理图片数）
                double currentSpeed = 0.0;
                if (timeSinceLastUpdate >= 0.001) { // 至少1毫秒
                    currentSpeed = static_cast<double>(newProgress) / timeSinceLastUpdate;
                }

                // 更新上次更新时间和处理数量
                lastUpdateTime_ = currentTime;
                lastTotalProcessed_ = totalProcessed;

                // 调用进度回调函数
                if (progressCallback_) {
                    progressCallback_(totalProcessed, totalImages_.load());
                }

                // 更新Windows报告器的处理进度
                if (windowsReporter_ && windowsReporter_->isEnabled()) {
                    windowsReporter_->updateProcessedImages(totalProcessed);
                } else if (windowsReporter_ && !windowsReporter_->isEnabled()) {
                    // 如果报告器已禁用，每10秒尝试重置一次
                    static auto lastResetTime = std::chrono::steady_clock::now();
                    auto now = std::chrono::steady_clock::now();
                    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - lastResetTime).count();

                    if (elapsed >= 10) {
                        spdlog::info("尝试重置Windows报告器...");
                        windowsReporter_->resetErrorCount();
                        lastResetTime = now;
                    }
                }

                // 打印进度
                std::lock_guard<std::mutex> lock(cout_mutex);
                spdlog::info("进程 {} 已处理 {} 张图片", processId, count);
                spdlog::info("总进度: {}/{} ({:.2f}%)",
                    totalProcessed, imagePaths.size(), (totalProcessed * 100.0 / imagePaths.size()));
                spdlog::info("处理速度: {:.2f} 图片/秒 (当前), {:.2f} 图片/秒 (平均)",
                    currentSpeed, overallSpeed);
                spdlog::info("已用时间: {} 秒", elapsedSeconds);
                spdlog::info("预计剩余时间: {} 秒",
                    (overallSpeed > 0 ? static_cast<int>((imagePaths.size() - totalProcessed) / overallSpeed) : 0));
                spdlog::info("---------------------------------------------------");
            }
            else if (msg.type == ProcessMessage::Type::COMPLETE) {
                // 子进程完成了一批任务，分配新的任务
                spdlog::info("进程 {} 完成了一批任务，正在分配新任务", processId);

                // 如果任务队列不为空，分配新任务
                if (!taskQueue_.empty()) {
                    distributeBatchToProcess(processId);
                } else {
                    spdlog::info("没有更多任务可分配给进程 {}", processId);

                    // 如果所有图片都已处理完成，向该进程发送终止信号
                    if (totalProcessed >= imagePaths.size()) {
                        // 发送终止信号
                        ProcessMessage terminateMsg;
                        terminateMsg.type = ProcessMessage::Type::TERMINATE;
                        terminateMsg.process_id = processId;

                        std::string serialized = serializeMessage(terminateMsg);
                        zmq::message_t terminateIdentity(std::to_string(processId));
                        socket_.send(terminateIdentity, zmq::send_flags::sndmore);
                        zmq::message_t terminateMessage(serialized);
                        socket_.send(terminateMessage, zmq::send_flags::none);

                        spdlog::info("已向进程 {} 发送终止信号", processId);
                    }

                    // 标记该进程已完成所有任务
                    if (!processCompleted[processId]) {
                        processCompleted[processId] = true;
                        completedProcesses++;
                        spdlog::info("进程 {} 已完成所有任务，已完成进程数: {}/{}",
                            processId, completedProcesses, numProcesses_);
                    }
                }
            }
        }
    }
    catch (const std::exception& e) {
        spdlog::error("监控进度时出错: {}", e.what());
    }

    // 停止Windows报告器
    if (windowsReporter_) {
        spdlog::info("正在停止Windows报告器...");
        windowsReporter_->stop();
    }

    // 确保终止所有子进程
    spdlog::info("正在终止所有工作进程...");
    terminateWorkerProcesses();

    // 等待所有子进程结束，设置超时
    spdlog::info("等待所有工作进程结束...");
    auto waitStart = std::chrono::high_resolution_clock::now();
    const int MAX_WAIT_SECONDS = 10; // 最多等待10秒

    for (auto& process : processes_) {
        // 等待进程结束，但设置超时
        bool exited = false;
        while (!exited) {
            exited = process.wait_for(std::chrono::milliseconds(100));

            auto waitElapsed = std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::high_resolution_clock::now() - waitStart).count();

            if (waitElapsed > MAX_WAIT_SECONDS) {
                spdlog::warn("等待工作进程结束超时，强制终止");
                break;
            }
        }
    }

    // 计算总处理时间和平均速度
    auto endTime = std::chrono::high_resolution_clock::now();
    auto totalElapsedSeconds = std::chrono::duration_cast<std::chrono::seconds>(endTime - startTime_).count();
    int finalProcessed = totalProcessed_.load();
    double averageSpeed = (totalElapsedSeconds > 0) ? static_cast<double>(finalProcessed) / totalElapsedSeconds : 0.0;

    spdlog::info("====================================================");
    spdlog::info("所有进程已完成。总共处理: {} 张图片", finalProcessed);
    spdlog::info("总处理时间: {} 秒", totalElapsedSeconds);
    spdlog::info("平均处理速度: {:.2f} 图片/秒", averageSpeed);
    spdlog::info("====================================================");

    // 调用完成回调函数
    if (completionCallback_) {
        std::stringstream message;
        message << "处理完成。总共处理: " << finalProcessed << " 张图片，用时: " << totalElapsedSeconds << " 秒";
        completionCallback_(true, message.str());
    }

    return 0;
}

bool MainProcess::startWorkerProcesses() {
    try {
        // 如果没有指定GPU ID，则使用默认方式启动进程
        if (gpu_ids_.empty()) {
            spdlog::info("未指定GPU ID，使用默认方式启动 {} 个工作进程", numProcesses_);
            for (int i = 0; i < numProcesses_; ++i) {
                std::string workerLogFile = "worker_" + std::to_string(i) + ".log";
                spdlog::info("启动工作进程 {}，日志文件: {}", i, workerLogFile);
                processes_.emplace_back(
                    "./worker_process",
                    boost::process::args = {std::to_string(i), outputDir_, "tcp://localhost:5555", workerLogFile},
                    boost::process::std_out > boost::process::null,
                    boost::process::std_err > boost::process::null
                );
            }
        } else {
            // 根据指定的GPU ID启动进程
            spdlog::info("根据指定的GPU ID启动工作进程");
            for (size_t i = 0; i < gpu_ids_.size(); ++i) {
                int gpu_id = gpu_ids_[i];
                std::string workerLogFile = "worker_gpu" + std::to_string(gpu_id) + ".log";

                // 设置环境变量，限制进程只能看到一个GPU
                std::string cuda_visible_devices = std::to_string(gpu_id);

                spdlog::info("启动工作进程 {} 在 GPU {} 上，日志文件: {}", i, gpu_id, workerLogFile);

                // 创建环境变量映射
                boost::process::environment env = boost::process::environment();
                env["CUDA_VISIBLE_DEVICES"] = cuda_visible_devices;

                processes_.emplace_back(
                    "./worker_process",
                    boost::process::args = {std::to_string(i), outputDir_, "tcp://localhost:5555", workerLogFile},
                    boost::process::std_out > boost::process::null,
                    boost::process::std_err > boost::process::null,
                    env
                );
            }

            // 更新进程数量为实际启动的进程数
            numProcesses_ = static_cast<int>(gpu_ids_.size());
        }
        return true;
    } catch (const std::exception& e) {
        spdlog::error("启动工作进程时出错: {}", e.what());
        return false;
    }
}

bool MainProcess::initialDistributeTasks() {
    try {
        // 首先，向所有工作进程发送 YOLOv8 配置信息
        sendYoloConfigToWorkers();

        // 计算每个进程应该分配的任务数量
        size_t totalTasks = taskQueue_.size();
        size_t tasksPerProcess = totalTasks / numProcesses_;
        size_t remainingTasks = totalTasks % numProcesses_;

        spdlog::info("初始分发任务：总任务数 {}，每个进程分配 {} 个任务", totalTasks, tasksPerProcess);

        // 创建临时队列，用于重新分配任务
        std::queue<std::string> tempQueue;

        // 为每个进程准备任务批次
        std::vector<std::vector<std::string>> processBatches(numProcesses_);

        // 从任务队列中取出所有任务
        while (!taskQueue_.empty()) {
            tempQueue.push(taskQueue_.front());
            taskQueue_.pop();
        }

        // 均匀分配任务给每个进程
        for (int i = 0; i < numProcesses_; ++i) {
            size_t processTaskCount = tasksPerProcess + (i < remainingTasks ? 1 : 0);

            for (size_t j = 0; j < processTaskCount && !tempQueue.empty(); ++j) {
                processBatches[i].push_back(tempQueue.front());
                tempQueue.pop();
            }
        }

        // 将剩余的任务放回队列
        while (!tempQueue.empty()) {
            taskQueue_.push(tempQueue.front());
            tempQueue.pop();
        }

        // 为每个进程分配初始任务
        for (int i = 0; i < numProcesses_; ++i) {
            if (processBatches[i].empty()) {
                spdlog::warn("进程 {} 没有收到初始任务", i);
                continue;
            }

            // 创建任务消息
            nlohmann::json taskJson;
            taskJson["image_paths"] = processBatches[i];

            ProcessMessage msg;
            msg.type = ProcessMessage::Type::TASK;
            msg.process_id = i;
            msg.data = taskJson.dump();
            msg.count = processBatches[i].size();

            std::string serialized = serializeMessage(msg);

            // 发送给特定进程
            zmq::message_t identity(std::to_string(i));
            socket_.send(identity, zmq::send_flags::sndmore);
            zmq::message_t message(serialized);
            socket_.send(message, zmq::send_flags::none);

            spdlog::info("发送了 {} 个任务给进程 {}", processBatches[i].size(), i);
        }

        return true;
    } catch (const std::exception& e) {
        spdlog::error("初始分发任务时出错: {}", e.what());
        return false;
    }
}

bool MainProcess::distributeBatchToProcess(int processId) {
    try {
        // 检查任务队列是否为空
        if (taskQueue_.empty()) {
            return false;
        }

        // 准备一批任务
        std::vector<std::string> batch;
        int count = 0;
        while (!taskQueue_.empty() && count < batchSize_) {
            batch.push_back(taskQueue_.front());
            taskQueue_.pop();
            count++;
        }

        // 如果没有任务可分配，返回失败
        if (batch.empty()) {
            return false;
        }

        // 创建任务消息
        nlohmann::json taskJson;
        taskJson["image_paths"] = batch;

        ProcessMessage msg;
        msg.type = ProcessMessage::Type::TASK;
        msg.process_id = processId;
        msg.data = taskJson.dump();
        msg.count = batch.size();

        std::string serialized = serializeMessage(msg);

        // 发送给特定进程
        zmq::message_t identity(std::to_string(processId));
        socket_.send(identity, zmq::send_flags::sndmore);
        zmq::message_t message(serialized);
        socket_.send(message, zmq::send_flags::none);

        spdlog::info("发送了 {} 个任务给进程 {}", batch.size(), processId);
        return true;
    } catch (const std::exception& e) {
        spdlog::error("分发任务给进程 {} 时出错: {}", processId, e.what());
        return false;
    }
}

// monitorProgress方法已集成到run方法中

void MainProcess::terminateWorkerProcesses() {
    // 发送终止信号给所有子进程
    for (int i = 0; i < numProcesses_; ++i) {
        ProcessMessage msg;
        msg.type = ProcessMessage::Type::TERMINATE;
        msg.process_id = i;

        std::string serialized = serializeMessage(msg);

        zmq::message_t identity(std::to_string(i));
        socket_.send(identity, zmq::send_flags::sndmore);
        zmq::message_t message(serialized);
        socket_.send(message, zmq::send_flags::none);
    }
}

// 解析配置文件中的 gpu_id 配置项
void MainProcess::parse_gpu_ids() {
    // 如果客户端已经指定了GPU ID，则验证客户端指定的GPU ID
    if (!gpu_ids_.empty()) {
        spdlog::info("使用客户端指定的 GPU ID");
        validateGpuIds();
        return;
    }

    try {
        // 尝试打开并解析JSON配置文件
        std::ifstream file(config_file_);
        if (!file.is_open()) {
            spdlog::error("无法打开配置文件 {}", config_file_);
            return;
        }

        nlohmann::json config;
        file >> config;
        file.close();

        // 检查配置文件中是否有 gpu_ids 字段
        if (config.contains("gpu") && config["gpu"].contains("gpu_ids")) {
            auto gpu_ids = config["gpu"]["gpu_ids"];
            if (gpu_ids.is_array()) {
                for (const auto& id : gpu_ids) {
                    gpu_ids_.push_back(id.get<int>());
                }
            } else if (gpu_ids.is_string()) {
                // 如果是字符串，按逗号分隔解析
                std::string gpu_id_str = gpu_ids.get<std::string>();
                std::stringstream ss(gpu_id_str);
                std::string item;
                while (std::getline(ss, item, ',')) {
                    try {
                        int gpu_id = std::stoi(item);
                        gpu_ids_.push_back(gpu_id);
                    } catch (const std::exception& e) {
                        spdlog::error("解析 GPU ID 失败: {}", item);
                    }
                }
            }
        }
    } catch (const std::exception& e) {
        spdlog::error("解析配置文件中的 GPU ID 失败: {}", e.what());
    }

    // 如果没有找到任何 GPU ID，默认使用 GPU 0
    if (gpu_ids_.empty()) {
        spdlog::warn("配置文件中未找到 GPU ID，默认使用 GPU 0");
        gpu_ids_.push_back(0);
    }

    // 验证解析出的GPU ID
    validateGpuIds();
}

// 向所有工作进程发送 YOLOv8 配置信息
void MainProcess::sendYoloConfigToWorkers() {
    try {
        // 从配置文件中读取 YOLOv8 配置
        ConfigParser parser;
        if (!parser.loadFromFile(config_file_)) {
            spdlog::error("无法加载配置文件: {}", config_file_);
            return;
        }

        // 读取 YOLOv8 配置
        std::string engine_file = parser.getString("yolo", "engine_file",
            "/home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.transd.engine");
        std::string config_file = parser.getString("yolo", "config_file",
            "/home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.cfg");
        std::string task_name = parser.getString("yolo", "task_name", "yolov8_detection");
        std::string log_file = parser.getString("yolo", "log_file", "yolov8_detection.log");

        spdlog::info("向工作进程发送 YOLOv8 配置...");
        spdlog::info("引擎文件: {}", engine_file);
        spdlog::info("配置文件: {}", config_file);
        spdlog::info("任务名称: {}", task_name);
        spdlog::info("日志文件: {}", log_file);

        // 创建配置消息
        nlohmann::json configJson;
        configJson["engine_file"] = engine_file;
        configJson["config_file"] = config_file;
        configJson["task_name"] = task_name;
        configJson["log_file"] = log_file;

        // 向每个工作进程发送配置信息
        for (int i = 0; i < numProcesses_; ++i) {
            ProcessMessage msg;
            msg.type = ProcessMessage::Type::CONFIG;
            msg.process_id = i;
            msg.data = configJson.dump();

            std::string serialized = serializeMessage(msg);

            zmq::message_t identity(std::to_string(i));
            socket_.send(identity, zmq::send_flags::sndmore);
            zmq::message_t message(serialized);
            socket_.send(message, zmq::send_flags::none);

            spdlog::info("已向进程 {} 发送 YOLOv8 配置", i);
        }
    } catch (const std::exception& e) {
        spdlog::error("向工作进程发送 YOLOv8 配置时出错: {}", e.what());
    }
}

// 验证GPU ID是否有效
void MainProcess::validateGpuIds() {
    if (gpu_ids_.empty()) {
        return;
    }

    // 获取系统中实际的GPU数量
    int actualGpuCount = 0;
    std::string gpuInfo;
    if (!GpuDetector::detectGpu(actualGpuCount, gpuInfo)) {
        spdlog::error("无法检测系统中的GPU数量，跳过GPU ID验证");
        return;
    }

    spdlog::info("系统中检测到 {} 个GPU", actualGpuCount);

    // 检查配置的GPU ID是否超出范围
    std::vector<int> validGpuIds;
    std::vector<int> invalidGpuIds;

    for (int gpu_id : gpu_ids_) {
        if (gpu_id >= 0 && gpu_id < actualGpuCount) {
            validGpuIds.push_back(gpu_id);
        } else {
            invalidGpuIds.push_back(gpu_id);
        }
    }

    // 如果有无效的GPU ID，记录警告
    if (!invalidGpuIds.empty()) {
        std::string invalidIds;
        for (size_t i = 0; i < invalidGpuIds.size(); ++i) {
            if (i > 0) invalidIds += ", ";
            invalidIds += std::to_string(invalidGpuIds[i]);
        }
        spdlog::warn("配置中包含无效的GPU ID: [{}]，系统只有 {} 个GPU (ID范围: 0-{})",
                    invalidIds, actualGpuCount, actualGpuCount - 1);
    }

    // 如果所有GPU ID都无效，使用默认GPU 0
    if (validGpuIds.empty()) {
        spdlog::error("所有配置的GPU ID都无效，回退到使用GPU 0");
        gpu_ids_ = {0};
        return;
    }

    // 如果有部分GPU ID无效，只使用有效的GPU ID
    if (validGpuIds.size() != gpu_ids_.size()) {
        std::string validIds;
        for (size_t i = 0; i < validGpuIds.size(); ++i) {
            if (i > 0) validIds += ", ";
            validIds += std::to_string(validGpuIds[i]);
        }
        spdlog::warn("只使用有效的GPU ID: [{}]", validIds);
        gpu_ids_ = validGpuIds;
    } else {
        spdlog::info("所有配置的GPU ID都有效");
    }

    // 去重GPU ID
    std::sort(gpu_ids_.begin(), gpu_ids_.end());
    gpu_ids_.erase(std::unique(gpu_ids_.begin(), gpu_ids_.end()), gpu_ids_.end());

    if (gpu_ids_.size() > 0) {
        std::string finalIds;
        for (size_t i = 0; i < gpu_ids_.size(); ++i) {
            if (i > 0) finalIds += ", ";
            finalIds += std::to_string(gpu_ids_[i]);
        }
        spdlog::info("最终使用的GPU ID: [{}]", finalIds);
    }
}

} // namespace mpf
