#include "mpf/windows_reporter.h"
#include "mpf/logger.h"
#include <nlohmann/json.hpp>
#include <experimental/filesystem>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <cstring>

namespace fs = std::experimental::filesystem;

namespace mpf {

WindowsReporter::WindowsReporter(const std::string& windowsHost, int windowsPort, const std::string& outputDir,
                               int maxRetries, int retryIntervalMs, int connectionTimeoutMs)
    : windowsHost_(windowsHost),
      windowsPort_(windowsPort),
      outputDir_(outputDir),
      running_(false),
      totalImages_(0),
      processedImages_(0),
      maxRetries_(maxRetries),
      retryIntervalMs_(retryIntervalMs),
      connectionTimeoutMs_(connectionTimeoutMs),
      errorCount_(0),
      enabled_(true) {

    // 初始化上次成功时间
    lastSuccessTime_ = std::chrono::steady_clock::now();
}

WindowsReporter::~WindowsReporter() {
    stop();
}

bool WindowsReporter::start() {
    if (running_) {
        spdlog::warn("Windows报告器已经在运行");
        return false;
    }

    running_ = true;
    reportThread_ = std::thread(&WindowsReporter::reportThread, this);
    spdlog::info("Windows报告器已启动，目标: {}:{}", windowsHost_, windowsPort_);
    return true;
}

void WindowsReporter::stop() {
    if (running_) {
        running_ = false;
        if (reportThread_.joinable()) {
            reportThread_.join();
        }
        spdlog::info("Windows报告器已停止");
    }
}

void WindowsReporter::setTotalImages(int totalImages) {
    totalImages_ = totalImages;
}

void WindowsReporter::updateProcessedImages(int processedImages) {
    processedImages_ = processedImages;
}

bool WindowsReporter::isEnabled() const {
    return enabled_.load();
}

void WindowsReporter::resetErrorCount() {
    errorCount_ = 0;
    enabled_ = true;
    spdlog::info("Windows报告器错误计数已重置，报告器已重新启用");
}

void WindowsReporter::reportThread() {
    while (running_) {
        try {
            // 如果报告器已禁用，等待一段时间后尝试重新连接
            if (!enabled_) {
                auto now = std::chrono::steady_clock::now();
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now - lastSuccessTime_).count();

                // 如果距离上次成功发送时间超过重试间隔，尝试重新连接
                if (elapsed >= retryIntervalMs_) {
                    spdlog::info("尝试重新连接到Windows机器...");
                    if (tryReconnect()) {
                        spdlog::info("重新连接成功，报告器已重新启用");
                        enabled_ = true;
                        errorCount_ = 0;
                    } else {
                        spdlog::warn("重新连接失败，将在 {} 毫秒后重试", retryIntervalMs_);
                        std::this_thread::sleep_for(std::chrono::milliseconds(retryIntervalMs_));
                        continue;
                    }
                } else {
                    // 等待剩余的重试间隔时间
                    int waitTime = retryIntervalMs_ - static_cast<int>(elapsed);
                    std::this_thread::sleep_for(std::chrono::milliseconds(waitTime));
                    continue;
                }
            }

            // 统计已处理图片数量
            int processedCount = countProcessedImages();

            // 构建消息
            nlohmann::json message;
            message["processed_images"] = processedCount;
            message["total_images"] = totalImages_.load();
            message["timestamp"] = std::chrono::system_clock::now().time_since_epoch().count();

            // 将消息添加到队列
            std::string messageStr = message.dump();
            enqueueMessage(messageStr);

            // 处理消息队列
            processMessageQueue();

            // 每秒发送一次
            std::this_thread::sleep_for(std::chrono::seconds(1));
        } catch (const std::exception& e) {
            spdlog::error("报告线程异常: {}", e.what());
            std::this_thread::sleep_for(std::chrono::seconds(5)); // 出错后等待5秒再重试
        }
    }
}

int WindowsReporter::countProcessedImages() {
    int count = 0;

    try {
        // 检查输出目录是否存在
        if (!fs::exists(outputDir_)) {
            return 0;
        }

        // 检查子目录
        std::vector<std::string> subDirs = {"T-L", "T-M", "T-R", "default"};

        for (const auto& subDir : subDirs) {
            std::string dirPath = outputDir_ + "/" + subDir;

            if (fs::exists(dirPath)) {
                for (const auto& entry : fs::directory_iterator(dirPath)) {
                    if (fs::is_regular_file(entry.path()) &&
                        entry.path().extension().string() == ".json") {
                        count++;
                    }
                }
            }
        }
    } catch (const std::exception& e) {
        spdlog::error("统计处理图片数量时出错: {}", e.what());
    }

    return count;
}

void WindowsReporter::enqueueMessage(const std::string& message) {
    std::lock_guard<std::mutex> lock(mutex_);
    messageQueue_.push(message);
    cv_.notify_one();
}

void WindowsReporter::processMessageQueue() {
    std::unique_lock<std::mutex> lock(mutex_);

    // 如果队列为空，直接返回
    if (messageQueue_.empty()) {
        return;
    }

    // 获取队列中的第一条消息
    std::string message = messageQueue_.front();
    messageQueue_.pop();

    // 解锁互斥锁，以便其他线程可以继续添加消息
    lock.unlock();

    // 发送消息
    if (sendMessage(message)) {
        // 发送成功，更新上次成功时间
        lastSuccessTime_ = std::chrono::steady_clock::now();
        errorCount_ = 0;
    } else {
        // 发送失败，增加错误计数
        errorCount_++;
        spdlog::error("向Windows机器发送消息失败，错误计数: {}/{}", errorCount_.load(), maxRetries_);

        // 如果错误计数达到最大重试次数，禁用报告器
        if (errorCount_ >= maxRetries_) {
            spdlog::error("错误计数达到最大值，Windows报告器已禁用");
            enabled_ = false;
        }
    }
}

bool WindowsReporter::tryReconnect() {
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock < 0) {
        spdlog::error("创建套接字失败");
        return false;
    }

    struct sockaddr_in serv_addr;
    memset(&serv_addr, 0, sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(windowsPort_);

    // 设置连接超时
    struct timeval timeout;
    timeout.tv_sec = connectionTimeoutMs_ / 1000;
    timeout.tv_usec = (connectionTimeoutMs_ % 1000) * 1000;
    if (setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout)) < 0) {
        spdlog::error("设置套接字选项失败");
        close(sock);
        return false;
    }

    // 转换IP地址
    if (inet_pton(AF_INET, windowsHost_.c_str(), &serv_addr.sin_addr) <= 0) {
        spdlog::error("无效的IP地址: {}", windowsHost_);
        close(sock);
        return false;
    }

    // 尝试连接
    if (connect(sock, (struct sockaddr *)&serv_addr, sizeof(serv_addr)) < 0) {
        spdlog::error("连接到Windows机器失败: {}:{}", windowsHost_, windowsPort_);
        close(sock);
        return false;
    }

    // 连接成功
    close(sock);
    return true;
}

bool WindowsReporter::sendMessage(const std::string& message) {
    // 如果报告器已禁用，直接返回失败
    if (!enabled_) {
        return false;
    }

    int sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock < 0) {
        spdlog::error("创建套接字失败");
        return false;
    }

    struct sockaddr_in serv_addr;
    memset(&serv_addr, 0, sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(windowsPort_);

    // 设置连接超时
    struct timeval timeout;
    timeout.tv_sec = connectionTimeoutMs_ / 1000;
    timeout.tv_usec = (connectionTimeoutMs_ % 1000) * 1000;
    if (setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout)) < 0) {
        spdlog::error("设置套接字选项失败");
        close(sock);
        return false;
    }

    // 转换IP地址
    if (inet_pton(AF_INET, windowsHost_.c_str(), &serv_addr.sin_addr) <= 0) {
        spdlog::error("无效的IP地址: {}", windowsHost_);
        close(sock);
        return false;
    }

    // 连接到服务器
    if (connect(sock, (struct sockaddr *)&serv_addr, sizeof(serv_addr)) < 0) {
        spdlog::error("连接到Windows机器失败: {}:{}", windowsHost_, windowsPort_);
        close(sock);
        return false;
    }

    // 发送消息
    if (send(sock, message.c_str(), message.length(), 0) < 0) {
        spdlog::error("发送消息失败");
        close(sock);
        return false;
    }

    close(sock);
    return true;
}

} // namespace mpf
