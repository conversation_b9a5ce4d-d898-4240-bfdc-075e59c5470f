#include "mpf/multi_process_framework.h"
#include <boost/program_options.hpp>
#include <iostream>

namespace po = boost::program_options;

namespace mpf {

bool loadConfig(const std::string& configFile,
               int& numProcesses,
               std::string& modelConfigFile,
               std::string& inputDir,
               std::string& outputDir,
               std::string& logFile,
               std::string& windowsHost,
               int& windowsPort,
               int& batchSize) {
    ConfigParser config;
    if (!config.loadFromFile(configFile)) {
        std::cerr << "无法加载配置文件: " << configFile << std::endl;
        return false;
    }

    // 读取配置项
    batchSize = config.getInt("process", "batch_size", 4);

    modelConfigFile = config.getString("paths", "model_config_dir", "/home/<USER>/workspace/maxi/test_small_imgs");
    inputDir = config.getString("paths", "input_dir", "/home/<USER>/workspace/maxi/test_small_imgs");
    outputDir = config.getString("paths", "output_dir", "/home/<USER>/workspace/maxi/test_results");
    logFile = config.getString("paths", "log_file", "multi_process_framework.log");

    // 根据GPU配置设置进程数量
    if (config.hasKey("gpu", "gpu_ids")) {
        try {
            auto gpuIds = config.getConfig()["gpu"]["gpu_ids"];
            if (gpuIds.is_array()) {
                // 如果是数组，进程数量等于GPU数量
                numProcesses = gpuIds.size();
            } else if (gpuIds.is_string()) {
                // 如果是字符串，按逗号分隔解析
                std::string gpuIdStr = gpuIds.get<std::string>();
                std::stringstream ss(gpuIdStr);
                std::string item;
                int count = 0;
                while (std::getline(ss, item, ',')) {
                    if (!item.empty()) {
                        count++;
                    }
                }
                numProcesses = count;
            } else {
                // 默认值
                numProcesses = 4;
            }
        } catch (const std::exception& e) {
            std::cerr << "解析GPU ID失败: " << e.what() << std::endl;
            numProcesses = 4;
        }
    } else {
        // 如果没有GPU配置，使用默认值
        numProcesses = 4;
    }

    bool windowsReporterEnabled = config.getBool("windows_reporter", "enabled", false);
    if (windowsReporterEnabled) {
        windowsHost = config.getString("windows_reporter", "host", "");
        windowsPort = config.getInt("windows_reporter", "port", 0);
    } else {
        windowsHost = "";
        windowsPort = 0;
    }

    return true;
}

bool parseCommandLine(int argc, char* argv[],
                     int& numProcesses,
                     std::string& modelConfigFile,
                     std::string& inputDir,
                     std::string& outputDir,
                     std::string& logFile,
                     std::string& windowsHost,
                     int& windowsPort,
                     int& batchSize) {
    try {
        // 默认配置文件路径
        std::string configFile = "config.json";

        // 解析命令行参数
        po::options_description desc("允许的选项");
        desc.add_options()
            ("help", "显示帮助信息")
            ("config", po::value<std::string>(&configFile), "配置文件路径")
            ("num-processes", po::value<int>(), "工作进程数量")
            ("batch-size", po::value<int>(), "每批任务数量")
            ("model-config-dir", po::value<std::string>(), "模型配置文件")
            ("input-dir", po::value<std::string>(), "输入目录")
            ("output-dir", po::value<std::string>(), "输出目录")
            ("log-file", po::value<std::string>(), "日志文件路径")
            ("windows-host", po::value<std::string>(), "Windows主机IP地址，用于发送处理进度")
            ("windows-port", po::value<int>(), "Windows主机端口，用于发送处理进度");

        po::variables_map vm;
        po::store(po::parse_command_line(argc, argv, desc), vm);
        po::notify(vm);

        if (vm.count("help")) {
            std::cout << desc << std::endl;
            return false;
        }

        // 首先从配置文件加载默认值
        if (fs::exists(configFile)) {
            if (!loadConfig(configFile, numProcesses, modelConfigFile, inputDir, outputDir, logFile, windowsHost, windowsPort, batchSize)) {
                std::cerr << "加载配置文件失败，将使用默认值" << std::endl;
                // 设置默认值
                numProcesses = 4;
                batchSize = 4;
                modelConfigFile = "/home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.cfg";
                inputDir = "/home/<USER>/workspace/maxi/test_small_imgs";
                outputDir = "/home/<USER>/workspace/maxi/test_results";
                logFile = "multi_process_framework.log";
                windowsHost = "";
                windowsPort = 0;
            }
        } else {
            // 设置默认值
            numProcesses = 4;
            batchSize = 4;
            modelConfigFile = "/home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.cfg";
            inputDir = "/home/<USER>/workspace/maxi/test_small_imgs";
            outputDir = "/home/<USER>/workspace/maxi/test_results";
            logFile = "multi_process_framework.log";
            windowsHost = "";
            windowsPort = 0;
        }

        // 命令行参数覆盖配置文件
        if (vm.count("num-processes")) {
            numProcesses = vm["num-processes"].as<int>();
        }

        if (vm.count("batch-size")) {
            batchSize = vm["batch-size"].as<int>();
        }

        if (vm.count("input-dir")) {
            inputDir = vm["input-dir"].as<std::string>();
        }

        if (vm.count("output-dir")) {
            outputDir = vm["output-dir"].as<std::string>();
        }

        if (vm.count("log-file")) {
            logFile = vm["log-file"].as<std::string>();
        }

        if (vm.count("windows-host")) {
            windowsHost = vm["windows-host"].as<std::string>();
        }

        if (vm.count("windows-port")) {
            windowsPort = vm["windows-port"].as<int>();
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "命令行解析错误: " << e.what() << std::endl;
        return false;
    }
}

} // namespace mpf
