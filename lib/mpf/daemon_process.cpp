#include "mpf/daemon_process.h"
#include "utils/logger.h"
#include "mpf/multi_process_framework.h"
#include "mpf/task_manager.h"
#include "mpf/config_parser.h"
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <signal.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <cstring>
#include <iostream>
#include <fstream>
#include <sstream>

namespace mpf {

// 全局指针，用于信号处理
static DaemonProcess* g_daemon = nullptr;

// 信号处理函数
void signalHandler(int signum) {
    spdlog::info("接收到信号 {}", signum);
    if (g_daemon) {
        g_daemon->stop();
    }
}

DaemonProcess::DaemonProcess(int listenPort, const std::string& configFile)
    : listenPort_(listenPort),
      configFile_(configFile),
      running_(false),
      processing_(false),
      currentClientSocket_(-1),
      numProcesses_(4),
      defaultInputDir_("/home/<USER>/workspace/maxi/test_small_imgs"),
      defaultOutputDir_("/home/<USER>/workspace/maxi/test_results"),
      logFile_("daemon_process.log"),
      batchSize_(4) {

    // 设置全局指针，用于信号处理
    g_daemon = this;

    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
}

DaemonProcess::~DaemonProcess() {
    stop();
    g_daemon = nullptr;
}

bool DaemonProcess::start() {
    if (running_) {
        spdlog::warn("守护进程已经在运行");
        return false;
    }

    // 初始化日志
    if (!Logger::initMainLogger(logFile_)) {
        std::cerr << "无法初始化日志系统" << std::endl;
        return false;
    }

    // 加载配置
    int windowsPort = 0;
    std::string windowsHost;
    std::string modelConfigFile;
    if (!loadConfig(configFile_, numProcesses_, modelConfigFile, defaultInputDir_, defaultOutputDir_,
                   logFile_, windowsHost, windowsPort, batchSize_)) {
        spdlog::warn("加载配置文件失败，将使用默认值");
    }

    // 创建PID文件
    std::ofstream pidFile("/tmp/daemon_process.pid");
    if (pidFile.is_open()) {
        pidFile << getpid();
        pidFile.close();
        spdlog::info("PID文件已创建: /tmp/daemon_process.pid");
    } else {
        spdlog::warn("无法创建PID文件");
    }

    // 启动监听线程
    running_ = true;
    listenThread_ = std::thread(&DaemonProcess::listenThread, this);

    spdlog::info("守护进程已启动，监听端口: {}", listenPort_);
    return true;
}

void DaemonProcess::stop() {
    if (running_) {
        running_ = false;

        // 停止当前处理任务
        if (processing_ && mainProcess_) {
            spdlog::info("正在停止当前处理任务...");
            // 这里应该有一个方法来安全地停止主进程
            mainProcess_.reset();
            processing_ = false;
        }

        // 等待监听线程结束
        if (listenThread_.joinable()) {
            listenThread_.join();
        }

        spdlog::info("守护进程已停止");
    }
}

int DaemonProcess::run() {
    // 初始化日志系统（在守护进程化之前）
    if (!Logger::initMainLogger(logFile_)) {
        std::cerr << "无法初始化日志系统" << std::endl;
        return 1;
    }

    // 加载配置（在守护进程化之前）
    int windowsPort = 0;
    std::string windowsHost;
    std::string modelConfigFile;
    if (!loadConfig(configFile_, numProcesses_, modelConfigFile, defaultInputDir_, defaultOutputDir_,
                   logFile_, windowsHost, windowsPort, batchSize_)) {
        spdlog::warn("加载配置文件失败，将使用默认值");
    }

    // 守护进程化
    if (!daemonize()) {
        std::cerr << "守护进程化失败" << std::endl;
        return 1;
    }

    // 启动守护进程
    running_ = true;
    listenThread_ = std::thread(&DaemonProcess::listenThread, this);

    spdlog::info("守护进程已启动，监听端口: {}", listenPort_);

    // 主线程等待条件变量通知
    std::unique_lock<std::mutex> lock(mutex_);
    cv_.wait(lock, [this]() { return !running_; });

    return 0;
}

bool DaemonProcess::daemonize() {
    // 创建子进程
    pid_t pid = fork();

    if (pid < 0) {
        std::cerr << "创建子进程失败" << std::endl;
        return false;
    }

    // 父进程退出
    if (pid > 0) {
        exit(0);
    }

    // 子进程继续

    // 创建新会话
    if (setsid() < 0) {
        std::cerr << "创建新会话失败" << std::endl;
        return false;
    }

    // 忽略SIGHUP信号
    signal(SIGHUP, SIG_IGN);

    // 再次fork，确保进程不是会话首进程
    pid = fork();

    if (pid < 0) {
        std::cerr << "第二次fork失败" << std::endl;
        return false;
    }

    // 父进程退出
    if (pid > 0) {
        exit(0);
    }

    // 子进程继续

    // 不再更改工作目录，保持在当前目录
    // 这样可以确保相对路径的配置文件能够被找到

    // 重设文件创建掩码
    umask(0);

    // 创建PID文件
    std::ofstream pidFile("/tmp/daemon_process.pid");
    if (pidFile.is_open()) {
        pidFile << getpid();
        pidFile.close();
    }

    // 重定向标准输入、输出和错误
    // 但不关闭所有文件描述符，这样日志文件可以继续工作
    int devNull = open("/dev/null", O_RDWR);
    dup2(devNull, STDIN_FILENO);
    dup2(devNull, STDOUT_FILENO);
    dup2(devNull, STDERR_FILENO);

    if (devNull > 2) {
        close(devNull);
    }

    return true;
}

void DaemonProcess::listenThread() {
    // 创建套接字
    int serverSocket = socket(AF_INET, SOCK_STREAM, 0);
    if (serverSocket < 0) {
        spdlog::error("创建套接字失败");
        running_ = false;
        cv_.notify_all();
        return;
    }

    // 设置套接字选项，允许地址重用
    int opt = 1;
    if (setsockopt(serverSocket, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        spdlog::error("设置套接字选项失败");
        close(serverSocket);
        running_ = false;
        cv_.notify_all();
        return;
    }

    // 绑定地址
    struct sockaddr_in serverAddr;
    memset(&serverAddr, 0, sizeof(serverAddr));
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(listenPort_);

    if (bind(serverSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) < 0) {
        spdlog::error("绑定地址失败");
        close(serverSocket);
        running_ = false;
        cv_.notify_all();
        return;
    }

    // 监听连接
    if (listen(serverSocket, 5) < 0) {
        spdlog::error("监听失败");
        close(serverSocket);
        running_ = false;
        cv_.notify_all();
        return;
    }

    spdlog::info("监听线程已启动，等待客户端连接...");

    // 接收连接
    while (running_) {
        // 设置超时，以便定期检查running_标志
        fd_set readfds;
        FD_ZERO(&readfds);
        FD_SET(serverSocket, &readfds);

        struct timeval timeout;
        timeout.tv_sec = 1;
        timeout.tv_usec = 0;

        int activity = select(serverSocket + 1, &readfds, NULL, NULL, &timeout);

        if (activity < 0 && errno != EINTR) {
            spdlog::error("select错误: {}", strerror(errno));
            break;
        }

        // 如果没有活动，继续循环
        if (activity <= 0) {
            continue;
        }

        // 接受新连接
        struct sockaddr_in clientAddr;
        socklen_t clientAddrLen = sizeof(clientAddr);
        int clientSocket = accept(serverSocket, (struct sockaddr*)&clientAddr, &clientAddrLen);

        if (clientSocket < 0) {
            spdlog::error("接受连接失败: {}", strerror(errno));
            continue;
        }

        // 获取客户端IP地址
        char clientIP[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &(clientAddr.sin_addr), clientIP, INET_ADDRSTRLEN);
        spdlog::info("接收到来自 {} 的连接", clientIP);

        // 处理客户端连接
        handleClient(clientSocket);
    }

    // 关闭服务器套接字
    close(serverSocket);
    spdlog::info("监听线程已结束");
}

void DaemonProcess::handleClient(int clientSocket) {
    // 如果已经有处理任务在进行，拒绝新的请求
    if (processing_) {
        sendResponse(clientSocket, false, "已有处理任务在进行，请稍后再试");
        close(clientSocket);
        return;
    }

    // 接收请求
    const int bufferSize = 4096;
    char buffer[bufferSize];

    // 设置接收超时
    struct timeval timeout;
    timeout.tv_sec = 5;  // 5秒超时
    timeout.tv_usec = 0;
    if (setsockopt(clientSocket, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
        spdlog::error("设置接收超时失败");
        close(clientSocket);
        return;
    }

    // 接收数据
    int bytesRead = recv(clientSocket, buffer, bufferSize - 1, 0);
    if (bytesRead <= 0) {
        if (bytesRead == 0) {
            spdlog::info("客户端关闭连接");
        } else {
            spdlog::error("接收数据失败: {}", strerror(errno));
        }
        close(clientSocket);
        return;
    }

    // 确保字符串以null结尾
    buffer[bytesRead] = '\0';

    // 解析JSON请求
    try {
        nlohmann::json request = nlohmann::json::parse(buffer);
        spdlog::info("接收到请求: {}", request.dump());

        // 处理请求
        processRequest(request, clientSocket);
    } catch (const std::exception& e) {
        spdlog::error("解析请求失败: {}", e.what());
        sendResponse(clientSocket, false, std::string("解析请求失败: ") + e.what());
        close(clientSocket);
    }
}

void DaemonProcess::processRequest(const nlohmann::json& request, int clientSocket) {
    // 检查请求类型
    if (!request.contains("type")) {
        sendResponse(clientSocket, false, "请求缺少type字段");
        close(clientSocket);
        return;
    }

    std::string type = request["type"];

    if (type == "process_images") {
        std::string modelConfigFile = configFile_;

        // 处理图片请求
        std::string inputDir = defaultInputDir_;
        std::string outputDir = defaultOutputDir_;
        std::vector<int> gpuIds;

        // 如果请求中包含输入/输出目录，使用请求中的值
        if (request.contains("input_dir")) {
            inputDir = request["input_dir"];
        }

        if (request.contains("output_dir")) {
            outputDir = request["output_dir"];
        }

        // 如果请求中包含GPU ID，解析并使用
        if (request.contains("gpu_id")) {
            auto& gpu_id_value = request["gpu_id"];

            // 处理不同类型的GPU ID输入
            if (gpu_id_value.is_array()) {
                // 如果是数组，直接转换
                gpuIds = gpu_id_value.get<std::vector<int>>();
            } else if (gpu_id_value.is_string()) {
                // 如果是字符串，按逗号分隔解析
                std::string gpu_id_str = gpu_id_value.get<std::string>();
                std::stringstream ss(gpu_id_str);
                std::string item;
                while (std::getline(ss, item, ',')) {
                    try {
                        int gpu_id = std::stoi(item);
                        gpuIds.push_back(gpu_id);
                    } catch (const std::exception& e) {
                        spdlog::error("解析 GPU ID 失败: {}", item);
                    }
                }
            } else if (gpu_id_value.is_number()) {
                // 如果是单个数字
                gpuIds.push_back(gpu_id_value.get<int>());
            }

            if (!gpuIds.empty()) {
                spdlog::info("客户端指定了 GPU ID: ");
                for (int id : gpuIds) {
                    spdlog::info("  - GPU {}", id);
                }
            }
        }

        // 启动图片处理任务
        if (startImageProcessing(inputDir, outputDir, clientSocket, gpuIds)) {
            // 成功启动，不关闭客户端套接字，因为需要继续发送进度更新
            currentClientSocket_ = clientSocket;
        } else {
            sendResponse(clientSocket, false, "启动图片处理任务失败");
            close(clientSocket);
        }
    } else if (type == "status") {
        // 查询状态请求
        nlohmann::json response;
        response["success"] = true;
        response["processing"] = processing_.load();

        if (processing_) {
            // TODO: 添加更多状态信息
            response["message"] = "正在处理图片";
        } else {
            response["message"] = "空闲";
        }

        // 发送响应
        std::string responseStr = response.dump();
        send(clientSocket, responseStr.c_str(), responseStr.length(), 0);
        close(clientSocket);
    } else {
        // 未知请求类型
        sendResponse(clientSocket, false, "未知请求类型: " + type);
        close(clientSocket);
    }
}

bool DaemonProcess::startImageProcessing(const std::string& inputDir, const std::string& outputDir, int clientSocket, const std::vector<int>& gpuIds) {
    // 如果已经有处理任务在进行，返回失败
    if (processing_) {
        return false;
    }

    try {
        // 从配置文件中读取 YOLOv8 配置
        ConfigParser parser;
        if (!parser.loadFromFile(configFile_)) {
            spdlog::error("无法加载配置文件: {}", configFile_);
            return false;
        }

        // 读取 YOLOv8 配置
        std::string engine_file = parser.getString("yolo", "engine_file",
            "/home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.transd.engine");
        std::string model_config_file = parser.getString("yolo", "config_file",
            "/home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.cfg");
        std::string task_name = parser.getString("yolo", "task_name", "yolov8_detection");
        std::string yolo_log_file = parser.getString("yolo", "log_file", "yolov8_detection.log");

        spdlog::info("设置 YOLOv8 配置...");
        spdlog::info("引擎文件: {}", engine_file);
        spdlog::info("配置文件: {}", model_config_file);
        spdlog::info("任务名称: {}", task_name);
        spdlog::info("日志文件: {}", yolo_log_file);

        // 设置 YOLOv8 配置
        setYoloConfig(engine_file, model_config_file, task_name, yolo_log_file);

        // 创建主进程，传入客户端指定的GPU ID
        mainProcess_ = std::make_unique<MainProcess>(
            numProcesses_, configFile_, inputDir, outputDir, logFile_, batchSize_, "127.0.0.1", 0, nullptr, nullptr, gpuIds);

        // 设置处理标志
        processing_ = true;

        // 发送响应
        sendResponse(clientSocket, true, "图片处理任务已启动");

        // 启动处理线程
        std::thread processingThread([this, clientSocket]() {
            try {
                // 运行主进程
                int result = mainProcess_->run();

                // 处理完成
                processing_ = false;

                // 发送完成通知
                if (result == 0) {
                    sendCompletionNotice(clientSocket, true, "图片处理任务已完成");
                } else {
                    sendCompletionNotice(clientSocket, false, "图片处理任务失败，返回码: " + std::to_string(result));
                }

                // 关闭客户端套接字
                close(clientSocket);
                currentClientSocket_ = -1;

                // 清理主进程
                mainProcess_.reset();
            } catch (const std::exception& e) {
                spdlog::error("处理线程异常: {}", e.what());
                processing_ = false;
                sendCompletionNotice(clientSocket, false, std::string("处理线程异常: ") + e.what());
                close(clientSocket);
                currentClientSocket_ = -1;
                mainProcess_.reset();
            }
        });

        // 分离线程，让它在后台运行
        processingThread.detach();

        return true;
    } catch (const std::exception& e) {
        spdlog::error("创建主进程失败: {}", e.what());
        return false;
    }
}

bool DaemonProcess::sendResponse(int clientSocket, bool success, const std::string& message) {
    nlohmann::json response;
    response["success"] = success;
    response["message"] = message;

    std::string responseStr = response.dump();
    return send(clientSocket, responseStr.c_str(), responseStr.length(), 0) > 0;
}

bool DaemonProcess::sendProgressUpdate(int clientSocket, int processed, int total) {
    nlohmann::json update;
    update["type"] = "progress";
    update["processed"] = processed;
    update["total"] = total;
    update["timestamp"] = std::chrono::system_clock::now().time_since_epoch().count();

    std::string updateStr = update.dump();
    return send(clientSocket, updateStr.c_str(), updateStr.length(), 0) > 0;
}

bool DaemonProcess::sendCompletionNotice(int clientSocket, bool success, const std::string& message) {
    nlohmann::json notice;
    notice["type"] = "completion";
    notice["success"] = success;
    notice["message"] = message;
    notice["timestamp"] = std::chrono::system_clock::now().time_since_epoch().count();

    std::string noticeStr = notice.dump();
    return send(clientSocket, noticeStr.c_str(), noticeStr.length(), 0) > 0;
}

} // namespace mpf
