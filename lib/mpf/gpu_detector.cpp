#include "mpf/gpu_detector.h"
#include "mpf/logger.h"
#include <cstdio>
#include <string>
#include <sstream>

namespace mpf {

bool GpuDetector::detectGpu(int& gpuCount, std::string& gpuInfo) {
    gpuCount = 0;
    gpuInfo = "Unknown";

    // 使用nvidia-smi命令获取GPU信息
    FILE* pipe = popen("nvidia-smi --query-gpu=count,name,memory.total --format=csv,noheader", "r");
    if (!pipe) {
        spdlog::error("无法执行nvidia-smi命令");
        return false;
    }

    char buffer[1024];
    std::string result = "";
    while (!feof(pipe)) {
        if (fgets(buffer, sizeof(buffer), pipe) != nullptr)
            result += buffer;
    }
    pclose(pipe);

    if (result.empty()) {
        spdlog::warn("未检测到GPU或nvidia-smi命令不可用");
        return false;
    }

    // 解析结果，获取GPU数量
    std::istringstream iss(result);
    std::string line;
    while (std::getline(iss, line)) {
        gpuCount++;
        gpuInfo = line;
    }

    return true;
}

} // namespace mpf
