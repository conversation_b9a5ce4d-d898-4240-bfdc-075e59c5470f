#include "utils/config_parser.h"
#include "utils/logger.h"
#include <fstream>
#include <experimental/filesystem>

namespace fs = std::experimental::filesystem;

namespace utils {

ConfigParser::ConfigParser() : loaded_(false) {
}

bool ConfigParser::loadFromFile(const std::string& configFile) {
    try {
        // 检查文件是否存在
        if (!fs::exists(configFile)) {
            spdlog::error("配置文件不存在: {}", configFile);
            return false;
        }

        // 读取文件内容
        std::ifstream file(configFile);
        if (!file.is_open()) {
            spdlog::error("无法打开配置文件: {}", configFile);
            return false;
        }

        // 解析JSON
        try {
            file >> config_;
            loaded_ = true;
            spdlog::info("成功加载配置文件: {}", configFile);
            return true;
        } catch (const nlohmann::json::parse_error& e) {
            spdlog::error("解析配置文件失败: {}", e.what());
            return false;
        }
    } catch (const std::exception& e) {
        spdlog::error("加载配置文件时出错: {}", e.what());
        return false;
    }
}

int ConfigParser::getInt(const std::string& section, const std::string& key, int defaultValue) const {
    if (!loaded_ || !hasKey(section, key)) {
        return defaultValue;
    }

    try {
        return config_[section][key].get<int>();
    } catch (const std::exception& e) {
        spdlog::warn("获取整数配置项 {}.{} 失败: {}", section, key, e.what());
        return defaultValue;
    }
}

std::string ConfigParser::getString(const std::string& section, const std::string& key, const std::string& defaultValue) const {
    if (!loaded_ || !hasKey(section, key)) {
        return defaultValue;
    }

    try {
        return config_[section][key].get<std::string>();
    } catch (const std::exception& e) {
        spdlog::warn("获取字符串配置项 {}.{} 失败: {}", section, key, e.what());
        return defaultValue;
    }
}

bool ConfigParser::getBool(const std::string& section, const std::string& key, bool defaultValue) const {
    if (!loaded_ || !hasKey(section, key)) {
        return defaultValue;
    }

    try {
        return config_[section][key].get<bool>();
    } catch (const std::exception& e) {
        spdlog::warn("获取布尔配置项 {}.{} 失败: {}", section, key, e.what());
        return defaultValue;
    }
}

double ConfigParser::getDouble(const std::string& section, const std::string& key, double defaultValue) const {
    if (!loaded_ || !hasKey(section, key)) {
        return defaultValue;
    }

    try {
        return config_[section][key].get<double>();
    } catch (const std::exception& e) {
        spdlog::warn("获取浮点数配置项 {}.{} 失败: {}", section, key, e.what());
        return defaultValue;
    }
}

std::vector<std::string> ConfigParser::getStringArray(const std::string& section, const std::string& key) const {
    std::vector<std::string> result;
    if (!loaded_ || !hasKey(section, key)) {
        return result;
    }

    try {
        return config_[section][key].get<std::vector<std::string>>();
    } catch (const std::exception& e) {
        spdlog::warn("获取字符串数组配置项 {}.{} 失败: {}", section, key, e.what());
        return result;
    }
}

bool ConfigParser::hasKey(const std::string& section, const std::string& key) const {
    if (!loaded_ || !config_.contains(section)) {
        return false;
    }

    return config_[section].contains(key);
}

} // namespace utils
