#include "utils/logger.h"
#include <iostream>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/fmt/ostr.h>

namespace utils {

std::shared_ptr<spdlog::logger> Logger::logger_ = nullptr;

bool Logger::initMainLogger(const std::string& logFile) {
    try {
        // 创建控制台和文件两个日志接收器
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        console_sink->set_level(spdlog::level::info);

        auto file_sink = std::make_shared<spdlog::sinks::basic_file_sink_mt>(logFile, true);
        file_sink->set_level(spdlog::level::trace);

        // 创建多接收器日志记录器
        std::vector<spdlog::sink_ptr> sinks {console_sink, file_sink};
        logger_ = std::make_shared<spdlog::logger>("multi_process", sinks.begin(), sinks.end());
        logger_->set_level(spdlog::level::trace);

        // 设置为默认日志记录器
        spdlog::set_default_logger(logger_);
        spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%t] %v");

        logger_->info("日志系统初始化完成，日志文件: {}", logFile);
        return true;
    } catch (const spdlog::spdlog_ex& ex) {
        std::cerr << "日志初始化失败: " << ex.what() << std::endl;
        return false;
    }
}

bool Logger::initWorkerLogger(int processId, const std::string& logFile) {
    try {
        // 创建控制台和文件两个日志接收器
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        console_sink->set_level(spdlog::level::info);

        auto file_sink = std::make_shared<spdlog::sinks::basic_file_sink_mt>(logFile, true);
        file_sink->set_level(spdlog::level::trace);

        // 创建多接收器日志记录器
        std::vector<spdlog::sink_ptr> sinks {console_sink, file_sink};
        logger_ = std::make_shared<spdlog::logger>("worker_" + std::to_string(processId), sinks.begin(), sinks.end());
        logger_->set_level(spdlog::level::trace);

        // 设置为默认日志记录器
        spdlog::set_default_logger(logger_);
        spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [进程 " + std::to_string(processId) + "] %v");

        logger_->info("工作进程 {} 日志系统初始化完成，日志文件: {}", processId, logFile);
        return true;
    } catch (const spdlog::spdlog_ex& ex) {
        std::cerr << "工作进程 " << processId << " 日志初始化失败: " << ex.what() << std::endl;
        return false;
    }
}

std::shared_ptr<spdlog::logger> Logger::getLogger() {
    return logger_;
}

} // namespace utils
