# 多进程图像处理框架

这是一个多进程图像处理框架，用于并行处理大量图像文件，集成了YOLOv8目标检测功能。

## 功能特点

- 多进程并行处理图像
- 自动检测并利用多GPU资源
- 基于GPU ID分配工作进程
- 集成YOLOv8目标检测算法
- 实时监控处理进度
- 支持向Windows机器发送处理进度报告
- 支持配置文件和命令行参数
- 守护进程模式支持

## 编译

```bash
mkdir -p build && cd build
cmake ..
make -j$(nproc)
```

## 使用方法

### 守护进程模式

```bash
# 以守护进程模式启动
./daemon_process --port 9000 --config ../config.json

# 以非守护进程模式启动（调试模式）
./daemon_process --port 9000 --config ../config.json --daemon false
```

### 命令行参数

```bash
./daemon_process [选项]
```

可用选项:
- `--help` : 显示帮助信息
- `--config <文件路径>` : 指定配置文件路径
- `--port <端口>` : 指定监听端口
- `--daemon <true|false>` : 是否以守护进程模式运行，默认为true

### 配置文件

程序默认会读取当前目录下的`config.json`文件。也可以通过`--config`参数指定配置文件路径。

配置文件示例:

```json
{
    "process": {
        "batch_size": 1000
    },
    "paths": {
        "model_config_dir": "/home/<USER>/workspace/maxi/model_config.json",
        "input_dir": "/home/<USER>/workspace/maxi/test_large_imgs",
        "output_dir": "/home/<USER>/workspace/maxi/test_results",
        "log_file": "multi_process_framework.log"
    },
    "gpu": {
        "gpu_ids": [0, 1]
    },
    "windows_reporter": {
        "enabled": true,
        "host": "127.0.0.1",
        "port": 8080,
        "report_interval_ms": 1000
    },
    "yolo": {
        "engine_file": "/home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.transd.engine",
        "config_file": "/home/<USER>/workspace/yolov8_detection/workspace/models/shanghai/shanghai.cfg",
        "task_name": "yolov8_detection",
        "log_file": "yolov8_detection.log"
    }
}
```

### 配置项说明

#### process部分

- `batch_size`: 每批分配给工作进程的任务数量

#### paths部分

- `model_config_dir`: 模型配置文件路径
- `input_dir`: 输入目录，包含待处理的图像文件
- `output_dir`: 输出目录，处理结果将保存在这里
- `log_file`: 日志文件路径

#### gpu部分

- `gpu_ids`: 要使用的GPU ID列表，如[0, 1]表示使用GPU 0和GPU 1

#### windows_reporter部分

- `enabled`: 是否启用Windows报告器
- `host`: Windows主机IP地址
- `port`: Windows主机端口
- `report_interval_ms`: 报告间隔（毫秒）

#### yolo部分

- `engine_file`: YOLOv8引擎文件路径
- `config_file`: YOLOv8配置文件路径
- `task_name`: 任务名称
- `log_file`: YOLOv8日志文件路径

## 客户端测试

为了测试守护进程，我们提供了一个简单的客户端测试脚本：

```bash
# 在项目根目录下运行
./client_test.sh
```

这个脚本会向守护进程发送处理请求，处理指定目录下的图片。

## 处理结果

处理结果将保存为与原图片同名的JSON文件，包含检测到的目标信息：
- 类别标签
- 类别名称
- 边界框坐标（左上角和右下角）
- 置信度

## 多GPU支持

框架会自动检测系统中的GPU数量，并根据配置文件中的`gpu_ids`设置或客户端请求中的`gpu_id`参数分配工作进程。每个工作进程会绑定到一个特定的GPU上，通过环境变量`CUDA_VISIBLE_DEVICES`限制其只能看到一个GPU。

## 注意事项

- 输入目录应包含T-L、T-M、T-R三个子目录，每个子目录中包含图像文件
- 输出目录将自动创建，包含与输入目录相同的子目录结构
- 命令行参数的优先级高于配置文件
- 确保YOLOv8引擎文件和配置文件路径正确
- 每个GPU只会分配一个工作进程

## 更多文档

- [环境配置指南](docs/env.md)
- [守护进程测试文档](docs/test_daemon.md)
- [项目架构说明](docs/structure.md)
- [客户端测试说明](client_test/README.md)
